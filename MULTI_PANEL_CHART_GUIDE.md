# Multi-Panel Trading Chart System

## Overview

The Strategy Builder now features a professional multi-panel trading chart interface similar to TradingView, with synchronized panels for price action, volume, and technical indicators.

## Features

### 🎯 Core Features
- **Multi-Panel Layout**: Separate synchronized panels for main chart, volume, RSI, and MACD
- **Professional Toolbar**: Symbol search, timeframe selection, and chart tools
- **Crosshair Synchronization**: Synchronized crosshair and zoom across all panels
- **Real-time Updates**: Support for live price updates and streaming data
- **Responsive Design**: Optimized for desktop and mobile devices

### 📊 Chart Panels

#### Main Price Panel
- Candlestick chart with OHLC data
- Support for multiple timeframes (1m, 5m, 15m, 1h, 4h, 1D)
- Price scale with current price display
- Volume-colored candles option

#### Volume Panel
- Histogram display of trading volume
- Color-coded bars (green for up, red for down)
- Synchronized with main chart timeline

#### RSI Panel
- Relative Strength Index indicator
- Reference lines at 30, 50, and 70 levels
- Configurable period (default: 14)
- Overbought/oversold highlighting

#### MACD Panel
- MACD line, Signal line, and Histogram
- Configurable parameters (12, 26, 9)
- Zero line reference
- Divergence analysis support

### 🛠 Advanced Features

#### Toolbar Components
- **Symbol Selector**: Search and switch between trading pairs
- **Timeframe Buttons**: Quick timeframe switching
- **Chart Tools**: Crosshair, trendlines, rectangles, Fibonacci
- **View Controls**: Reset zoom, fullscreen mode

#### Synchronization
- **Time Scale Sync**: All panels share the same time axis
- **Crosshair Sync**: Hover information across all panels
- **Zoom Sync**: Pan and zoom operations synchronized
- **Data Alignment**: Perfect alignment of all indicators

## Usage

### Basic Setup

1. **Include Required Files**:
```html
<link rel="stylesheet" href="css/style.css">
<link rel="stylesheet" href="css/multi-panel.css">
<script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
<script src="js/multi-panel-chart.js"></script>
```

2. **HTML Structure**:
```html
<div class="multi-panel-container">
    <div class="chart-panel main-panel">
        <div id="mainChart" class="chart-content"></div>
    </div>
    <div class="chart-panel volume-panel">
        <div id="volumeChart" class="chart-content"></div>
    </div>
    <!-- Additional panels... -->
</div>
```

### JavaScript API

#### Initialize Chart Manager
```javascript
const chartManager = new MultiPanelChartManager();
```

#### Load Data
```javascript
const ohlcvData = [
    {
        timestamp: 1640995200,
        open: 50000,
        high: 51000,
        low: 49500,
        close: 50500,
        volume: 1000000
    }
    // ... more data
];

const indicators = {
    rsi: [45.2, 52.1, 38.7, ...],
    macd: {
        macd: [12.5, -8.3, 15.2, ...],
        signal: [10.1, -5.8, 12.1, ...],
        histogram: [2.4, -2.5, 3.1, ...]
    }
};

chartManager.loadData(ohlcvData, indicators);
```

#### Real-time Updates
```javascript
// Enable real-time updates
chartManager.enableRealtimeUpdates(1000); // Update every 1 second

// Update single candle
chartManager.updateData(newCandleData);

// Disable real-time updates
chartManager.disableRealtimeUpdates();
```

#### Trading Marks
```javascript
const tradingMarks = [
    {
        timestamp: 1640995200,
        type: 'buy',
        price: 50000
    },
    {
        timestamp: 1640995800,
        type: 'sell',
        price: 51000
    }
];

chartManager.addTradingMarks(tradingMarks);
```

## Demo

A complete demo is available at `frontend/static/demo-multi-panel.html`. This demo includes:

- Sample data loading for BTC and ETH
- Interactive panel toggling
- Timeframe switching
- Symbol search functionality
- Real-time price simulation

To run the demo:
1. Open `demo-multi-panel.html` in a web browser
2. Click "Load Sample Data" to populate the charts
3. Use the toolbar to interact with the charts
4. Toggle panels on/off using the demo controls

## Customization

### Panel Configuration
```javascript
const chartConfigs = {
    main: {
        container: 'mainChart',
        height: 450,
        priceScale: true,
        timeScale: true
    },
    volume: {
        container: 'volumeChart',
        height: 120,
        priceScale: false,
        timeScale: false
    }
    // ... additional panels
};
```

### Styling
Customize the appearance by modifying `multi-panel.css`:

- Panel heights and colors
- Toolbar styling
- Chart themes and colors
- Responsive breakpoints

### Adding New Indicators
1. Add panel configuration to `chartConfigs`
2. Create HTML structure for the new panel
3. Initialize series in `initSeries()` method
4. Add data loading logic in `loadIndicators()` method

## Performance

### Optimization Features
- **Data Filtering**: Automatic removal of invalid data points
- **Efficient Updates**: Batch updates for better performance
- **Memory Management**: Proper cleanup and garbage collection
- **Responsive Rendering**: Optimized for different screen sizes

### Performance Monitoring
The system includes built-in performance monitoring:
- Data loading time tracking
- Memory usage optimization
- Render performance metrics

## Browser Support

- **Chrome**: 80+
- **Firefox**: 75+
- **Safari**: 13+
- **Edge**: 80+

## Dependencies

- **TradingView Lightweight Charts**: 4.2.1+
- **Modern Browser**: ES6+ support required

## Migration from Legacy Chart

The new multi-panel system is backward compatible with the existing single-panel chart. To migrate:

1. Update HTML to use new multi-panel structure
2. Replace `ChartManager` with `MultiPanelChartManager`
3. Update CSS imports to include `multi-panel.css`
4. Test with existing data and indicator APIs

## Troubleshooting

### Common Issues

1. **Charts not displaying**: Ensure TradingView library is loaded before initialization
2. **Synchronization issues**: Check that all panels are properly initialized
3. **Performance problems**: Reduce data size or disable real-time updates
4. **Mobile responsiveness**: Verify CSS media queries are applied

### Debug Mode
Enable debug logging:
```javascript
window.multiPanelChartManager.debugMode = true;
```

## Future Enhancements

- **Drawing Tools**: Advanced drawing and annotation tools
- **Custom Indicators**: Plugin system for custom technical indicators
- **Data Export**: Chart image and data export functionality
- **Alerts System**: Price and indicator-based alerts
- **Strategy Backtesting**: Integrated backtesting visualization

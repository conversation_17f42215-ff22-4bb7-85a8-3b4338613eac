-- Recreate the manual_marks table with the correct schema

DROP TABLE IF EXISTS manual_marks;

CREATE TABLE manual_marks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entry_timestamp DATETIME NOT NULL,
    entry_price DECIMAL(20, 8) NOT NULL,
    side ENUM('buy', 'sell') NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    notes TEXT,
    exit_timestamp DATETIME NULL,
    exit_price DECIMAL(20, 8) NULL,
    exit_quantity DECIMAL(20, 8) NULL,
    exit_notes TEXT,
    pnl DECIMAL(20, 8) NULL,
    status ENUM('open', 'partial', 'closed') DEFAULT 'open',
    entry_ohlcv_data JSON,
    exit_ohlcv_data JSON,
    entry_indicator_data JSON,
    exit_indicator_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_entry_timestamp (entry_timestamp),
    INDEX idx_exit_timestamp (exit_timestamp),
    INDEX idx_status (status),
    INDEX idx_side (side)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Verify the table was created
DESCRIBE manual_marks;

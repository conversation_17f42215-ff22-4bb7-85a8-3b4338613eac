<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Builder - Trading Chart Analysis</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://unpkg.com/lightweight-charts@4.2.0/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Strategy Builder</h1>
            <p>Interactive Trading Chart Analysis & Strategy Development</p>
        </header>

        <div class="controls-panel">
            <div class="control-group">
                <label for="exchange">Exchange:</label>
                <select id="exchange">
                    <option value="binance">Binance</option>
                    <option value="mexc">MEXC</option>
                </select>
            </div>

            <div class="control-group">
                <label for="symbol">Symbol:</label>
                <input type="text" id="symbol" placeholder="BTCUSDT" value="BTCUSDT">
            </div>

            <div class="control-group">
                <label for="timeframe">Timeframe:</label>
                <select id="timeframe">
                    <option value="1m">1 Minute</option>
                    <option value="3m">3 Minutes</option>
                    <option value="5m">5 Minutes</option>
                    <option value="15m">15 Minutes</option>
                    <option value="30m">30 Minutes</option>
                    <option value="1h" selected>1 Hour</option>
                    <option value="2h">2 Hours</option>
                    <option value="4h">4 Hours</option>
                    <option value="1d">1 Day</option>
                    <option value="1w">1 Week</option>
                </select>
            </div>

            <div class="control-group">
                <label for="limit">Candles:</label>
                <select id="limit">
                    <option value="100">100</option>
                    <option value="200">200</option>
                    <option value="500" selected>500</option>
                    <option value="1000">1000</option>
                </select>
            </div>

            <button id="fetchData" class="btn btn-primary">Fetch Data</button>
            <button id="loadData" class="btn btn-secondary">Load from DB</button>
            <button id="testChart" class="btn btn-info">Test Chart</button>
        </div>

        <div class="indicators-panel">
            <h3>Technical Indicators</h3>
            <div class="indicators-controls">
                <div class="indicator-group">
                    <label>
                        <input type="checkbox" id="rsi-enabled" checked>
                        RSI
                    </label>
                    <input type="number" id="rsi-period" value="14" min="2" max="100" placeholder="Period">
                </div>

                <div class="indicator-group">
                    <label>
                        <input type="checkbox" id="macd-enabled" checked>
                        MACD
                    </label>
                    <input type="number" id="macd-fast" value="12" min="1" max="50" placeholder="Fast">
                    <input type="number" id="macd-slow" value="26" min="1" max="100" placeholder="Slow">
                    <input type="number" id="macd-signal" value="9" min="1" max="50" placeholder="Signal">
                </div>

                <div class="indicator-group">
                    <label>
                        <input type="checkbox" id="ema-enabled" checked>
                        EMA
                    </label>
                    <input type="number" id="ema-period" value="20" min="1" max="200" placeholder="Period">
                </div>

                <div class="indicator-group">
                    <label>
                        <input type="checkbox" id="sma-enabled">
                        SMA
                    </label>
                    <input type="number" id="sma-period" value="50" min="1" max="200" placeholder="Period">
                </div>

                <button id="calculateIndicators" class="btn btn-secondary">Calculate Indicators</button>
            </div>
        </div>

        <div class="chart-container">
            <div id="chart" class="chart"></div>
            <div class="chart-info">
                <div id="chart-status">Ready to load data</div>
                <div id="crosshair-info"></div>
            </div>
        </div>

        <div class="trade-panel">
            <h3>Trade Marking</h3>
            <div class="trade-controls">
                <button id="markEntry" class="btn btn-success">Mark Entry</button>
                <button id="markExit" class="btn btn-danger">Mark Exit</button>
                <select id="entrySide">
                    <option value="buy">Buy</option>
                    <option value="sell">Sell</option>
                </select>
                <button id="completeTrade" class="btn btn-primary">Complete Trade</button>
            </div>
            
            <div class="trade-info">
                <div id="current-marks"></div>
                <div id="trade-summary"></div>
            </div>
        </div>

        <div class="data-panel">
            <h3>Data Export</h3>
            <div class="export-controls">
                <button id="exportMarks" class="btn btn-secondary">Export Marks</button>
                <button id="exportTrades" class="btn btn-secondary">Export Trades</button>
                <button id="viewStrategyLog" class="btn btn-info">View Strategy Log</button>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modal-body"></div>
        </div>
    </div>

    <script src="/static/js/chart.js"></script>
    <script src="/static/js/indicators.js"></script>
    <script src="/static/js/trades.js"></script>
    <script src="/static/js/app.js"></script>
</body>
</html>

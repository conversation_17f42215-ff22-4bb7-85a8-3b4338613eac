<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strategy Builder - Professional Trading Analysis</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/multi-panel.css">
    <script src="https://unpkg.com/lightweight-charts@4.2.1/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        /* Professional Trading Interface Styles */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Trebuchet MS', Arial, sans-serif;
            background: #131722;
            color: #d1d4dc;
            overflow-x: hidden;
        }

        .trading-interface {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Professional Header */
        .professional-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
            border-bottom: 1px solid #363c4e;
            min-height: 50px;
        }

        .brand-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand-name {
            font-size: 18px;
            font-weight: 700;
            color: #2962ff;
        }

        .symbol-display {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .current-symbol {
            font-size: 16px;
            font-weight: 700;
            color: #fff;
        }

        .current-price {
            font-size: 16px;
            font-weight: 600;
            color: #26a69a;
        }

        .price-change {
            font-size: 14px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
        }

        .price-change.positive {
            color: #26a69a;
            background: rgba(38, 166, 154, 0.1);
        }

        .price-change.negative {
            color: #ef5350;
            background: rgba(239, 83, 80, 0.1);
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .connection-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef5350;
        }

        .status-dot.connected {
            background: #26a69a;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Professional Chart Container */
        .professional-chart-container {
            flex: 1;
            position: relative;
            background: #131722;
            min-height: 500px;
        }

        #professional-tradingview-chart {
            width: 100%;
            height: 100%;
        }

        /* Strategy Builder Sidebar */
        .strategy-sidebar {
            position: fixed;
            top: 50px;
            right: -350px;
            width: 350px;
            height: calc(100vh - 50px);
            background: #1e222d;
            border-left: 1px solid #363c4e;
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .strategy-sidebar.open {
            right: 0;
        }

        .sidebar-toggle {
            position: fixed;
            top: 60px;
            right: 10px;
            z-index: 1001;
            padding: 8px 12px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
        }

        .sidebar-toggle:hover {
            background: #1e88e5;
        }

        /* Sidebar Sections */
        .sidebar-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #363c4e;
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section h4 {
            margin: 0 0 15px 0;
            color: #d1d4dc;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .control-group {
            margin-bottom: 12px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #b2b5be;
            font-size: 12px;
            font-weight: 600;
        }

        .control-group input,
        .control-group select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #444;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 13px;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #2962ff;
            box-shadow: 0 0 0 2px rgba(41, 98, 255, 0.2);
        }

        .indicator-group {
            margin-bottom: 15px;
            padding: 12px;
            background: #2a2e39;
            border-radius: 6px;
            border: 1px solid #363c4e;
        }

        .indicator-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            color: #d1d4dc;
            font-weight: 600;
        }

        .indicator-group input[type="checkbox"] {
            width: auto;
        }

        .indicator-group input[type="number"] {
            margin-top: 5px;
        }

        .indicator-params {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-top: 8px;
        }

        .indicator-params input {
            font-size: 12px;
            padding: 6px 8px;
        }

        .status-message {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-message.success {
            background: rgba(38, 166, 154, 0.1);
            border: 1px solid #26a69a;
            color: #26a69a;
        }

        .status-message.error {
            background: rgba(239, 83, 80, 0.1);
            border: 1px solid #ef5350;
            color: #ef5350;
        }

        .status-message.loading {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid #2962ff;
            color: #2962ff;
        }

        /* Indicator Tooltip Styles */
        .indicator-tooltip-popup {
            position: absolute;
            background: rgba(42, 46, 57, 0.95);
            border: 1px solid #363c4e;
            border-radius: 6px;
            padding: 12px;
            font-size: 12px;
            color: #d1d4dc;
            z-index: 1000;
            max-width: 250px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            display: none;
        }

        .indicator-tooltip-popup .tooltip-section {
            margin-bottom: 8px;
        }

        .indicator-tooltip-popup .tooltip-section:last-child {
            margin-bottom: 0;
        }

        .indicator-tooltip-popup strong {
            color: #26a69a;
            font-weight: 600;
        }

        .indicator-tooltip-popup br {
            line-height: 1.4;
        }

        .trade-controls,
        .export-controls {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .trade-controls select {
            margin: 5px 0;
        }

        .btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.2s ease;
            text-align: center;
        }

        .btn-primary {
            background: #2962ff;
            color: white;
        }

        .btn-primary:hover {
            background: #1e88e5;
        }

        .btn-secondary {
            background: #444;
            color: #b2b5be;
        }

        .btn-secondary:hover {
            background: #555;
            color: #fff;
        }

        .btn-success {
            background: #26a69a;
            color: white;
        }

        .btn-success:hover {
            background: #00897b;
        }

        .btn-danger {
            background: #ef5350;
            color: white;
        }

        .btn-danger:hover {
            background: #e53935;
        }

        .btn-info {
            background: #29b6f6;
            color: white;
        }

        .btn-info:hover {
            background: #0288d1;
        }

        .status.error {
            color: #ef5350;
        }

        .status.info {
            color: #26a69a;
        }

        /* Fetch Result Styling */
        .fetch-result {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-top: 10px;
        }

        .fetch-result.success {
            background: rgba(38, 166, 154, 0.1);
            border: 1px solid #26a69a;
            color: #26a69a;
        }

        .fetch-result.error {
            background: rgba(239, 83, 80, 0.1);
            border: 1px solid #ef5350;
            color: #ef5350;
        }

        .fetch-result.info {
            background: rgba(41, 98, 255, 0.1);
            border: 1px solid #2962ff;
            color: #2962ff;
        }
    </style>
</head>
<body>
    <div class="trading-interface">
        <!-- Professional Header -->
        <div class="professional-header">
            <div class="brand-info">
                <div class="brand-name">Strategy Builder</div>
                <div class="symbol-display">
                    <div class="current-symbol" id="header-symbol">BTCUSDT</div>
                    <div class="current-price" id="header-price">$0.00</div>
                    <div class="price-change positive" id="header-change">+0.00%</div>
                </div>
            </div>

            <div class="header-controls">
                <div class="symbol-selector">
                    <input type="text" class="symbol-input" id="main-symbol-input" placeholder="Symbol" value="BTCUSDT">
                    <button class="btn btn-primary" id="main-change-symbol">Change</button>
                </div>

                <div class="timeframe-buttons">
                    <button class="timeframe-btn" data-interval="1m">1m</button>
                    <button class="timeframe-btn" data-interval="5m">5m</button>
                    <button class="timeframe-btn active" data-interval="15m">15m</button>
                    <button class="timeframe-btn" data-interval="1h">1h</button>
                    <button class="timeframe-btn" data-interval="4h">4h</button>
                    <button class="timeframe-btn" data-interval="1d">1d</button>
                </div>

                <div class="connection-indicator">
                    <div class="status-dot" id="main-connection-dot"></div>
                    <span id="main-connection-text">Connecting...</span>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Professional Chart Container -->
            <div class="professional-chart-container">
                <div id="professional-tradingview-chart"></div>
            </div>
        </div>

        <!-- Strategy Builder Sidebar -->
        <button class="sidebar-toggle" id="sidebar-toggle">Strategy Tools</button>

        <div class="strategy-sidebar" id="strategy-sidebar">
            <div style="padding: 20px;">
                <h3 style="margin-top: 0; color: #2962ff;">Strategy Builder</h3>

                <!-- Strategy Management -->
                <div class="sidebar-section">
                    <h4>Strategy Management</h4>
                    <div class="control-group">
                        <label for="strategy-select">Current Strategy:</label>
                        <select id="strategy-select">
                            <option value="">Select Strategy...</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <input type="text" id="new-strategy-name" placeholder="New strategy name..." style="margin-bottom: 8px;">
                        <button id="create-strategy" class="btn btn-primary" style="width: 100%;">Create New Strategy</button>
                    </div>
                </div>

                <!-- Data Controls -->
                <div class="sidebar-section">
                    <h4>Data Controls</h4>
                    <div class="control-group">
                        <label for="exchange">Exchange:</label>
                        <select id="exchange">
                            <option value="binance">Binance</option>
                            <option value="mexc">MEXC</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="symbol">Symbol:</label>
                        <input type="text" id="symbol" placeholder="BTCUSDT" value="BTCUSDT">
                    </div>

                    <div class="control-group">
                        <label for="timeframe">Timeframe:</label>
                        <select id="timeframe">
                            <option value="1m">1 Minute</option>
                            <option value="3m">3 Minutes</option>
                            <option value="5m">5 Minutes</option>
                            <option value="15m" selected>15 Minutes</option>
                            <option value="30m">30 Minutes</option>
                            <option value="1h">1 Hour</option>
                            <option value="2h">2 Hours</option>
                            <option value="4h">4 Hours</option>
                            <option value="1d">1 Day</option>
                            <option value="1w">1 Week</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="limit">Candles:</label>
                        <select id="limit">
                            <option value="100">100</option>
                            <option value="200">200</option>
                            <option value="500" selected>500</option>
                            <option value="1000">1000</option>
                            <option value="2000">2000</option>
                            <option value="5000">5000</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="start-date">Start Date:</label>
                        <input type="datetime-local" id="start-date" style="width: 100%;">
                    </div>

                    <div class="control-group">
                        <label for="end-date">End Date:</label>
                        <input type="datetime-local" id="end-date" style="width: 100%;">
                    </div>

                    <div class="control-group">
                        <button id="fetchData" class="btn btn-primary">Fetch Data</button>
                        <button id="loadData" class="btn btn-secondary" disabled>Load from DB</button>
                        <button id="testChart" class="btn btn-info">Test Chart</button>
                    </div>

                    <div id="fetch-result" style="margin-top: 10px; padding: 8px; border-radius: 4px; font-size: 12px; display: none;"></div>
                </div>

                <!-- Technical Indicators -->
                <div class="sidebar-section">
                    <h4>Technical Indicators</h4>
                    <div class="indicators-controls">
                        <!-- EMA with multiple periods -->
                        <div class="indicator-group">
                            <label>
                                <input type="checkbox" id="ema-enabled" checked>
                                EMA (Exponential Moving Average)
                            </label>
                            <div class="indicator-params">
                                <input type="number" id="ema-period1" value="50" min="1" max="500" placeholder="Period 1">
                                <input type="number" id="ema-period2" value="100" min="1" max="500" placeholder="Period 2">
                                <input type="number" id="ema-period3" value="200" min="1" max="500" placeholder="Period 3">
                            </div>
                        </div>

                        <!-- Bollinger Bands -->
                        <div class="indicator-group">
                            <label>
                                <input type="checkbox" id="bollinger-enabled" checked>
                                Bollinger Bands
                            </label>
                            <div class="indicator-params">
                                <input type="number" id="bollinger-period" value="20" min="5" max="100" placeholder="Period">
                                <input type="number" id="bollinger-std" value="2" min="0.5" max="5" step="0.1" placeholder="Std Dev">
                            </div>
                        </div>

                        <!-- MACD -->
                        <div class="indicator-group">
                            <label>
                                <input type="checkbox" id="macd-enabled" checked>
                                MACD
                            </label>
                            <div class="indicator-params">
                                <input type="number" id="macd-fast" value="12" min="1" max="50" placeholder="Fast">
                                <input type="number" id="macd-slow" value="26" min="1" max="100" placeholder="Slow">
                                <input type="number" id="macd-signal" value="9" min="1" max="50" placeholder="Signal">
                            </div>
                        </div>

                        <!-- RSI with multiple periods -->
                        <div class="indicator-group">
                            <label>
                                <input type="checkbox" id="rsi-enabled" checked>
                                RSI (Relative Strength Index)
                            </label>
                            <div class="indicator-params">
                                <input type="number" id="rsi-period1" value="6" min="2" max="100" placeholder="Period 1">
                                <input type="number" id="rsi-period2" value="12" min="2" max="100" placeholder="Period 2">
                                <input type="number" id="rsi-period3" value="24" min="2" max="100" placeholder="Period 3">
                            </div>
                        </div>

                        <button id="plotIndicators" class="btn btn-primary">Plot Indicators</button>
                        <div id="indicator-status" class="status-message"></div>
                    </div>
                </div>

                <!-- Trade Management -->
                <div class="sidebar-section">
                    <h4>Trade Marking</h4>
                    <div class="trade-controls">
                        <button id="markEntry" class="btn btn-success">Mark Entry</button>
                        <button id="markExit" class="btn btn-danger">Mark Exit</button>
                        <select id="entrySide">
                            <option value="buy">Buy</option>
                            <option value="sell">Sell</option>
                        </select>
                        <button id="completeTrade" class="btn btn-primary">Complete Trade</button>
                    </div>

                    <div class="trade-info">
                        <div id="current-marks"></div>
                        <div id="trade-summary"></div>
                    </div>
                </div>

                <!-- Data Export -->
                <div class="sidebar-section">
                    <h4>Data Export</h4>
                    <div class="export-controls">
                        <button id="exportMarks" class="btn btn-secondary">Export Marks</button>
                        <button id="exportTrades" class="btn btn-secondary">Export Trades</button>
                        <button id="viewStrategyLog" class="btn btn-info">View Strategy Log</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar" style="display: flex; justify-content: space-between; align-items: center; padding: 6px 16px; background: #1e222d; border-top: 1px solid #363c4e; font-size: 12px; min-height: 32px;">
            <div style="display: flex; align-items: center; gap: 20px;">
                <div id="chart-status" class="status info">Initializing professional chart...</div>
                <div id="crosshair-info" style="font-family: 'Courier New', monospace; color: #b2b5be;"></div>
            </div>
            <div>
                <span>Powered by Binance API & TradingView Charts</span>
            </div>
        </div>
    </div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modal-body"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/static/js/tradingview-chart.js"></script>
    <script src="/static/js/strategy-manager.js"></script>
    <script src="/static/js/indicators.js"></script>
    <script src="/static/js/trades.js"></script>
    <script>
        // Professional TradingView Chart Integration for Strategy Builder
        let professionalChart = null;

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize professional chart
            const initProfessionalChart = () => {
                const container = document.getElementById('professional-tradingview-chart');
                if (!container) {
                    console.log('Chart container not ready, retrying...');
                    setTimeout(initProfessionalChart, 100);
                    return;
                }

                if (typeof LightweightCharts !== 'undefined') {
                    professionalChart = new TradingViewChart('professional-tradingview-chart', {
                        symbol: 'BTCUSDT',
                        interval: '15m',
                        theme: 'dark',
                        enableWebSocket: false,
                        enableInfiniteHistory: false
                    });

                    window.professionalChart = professionalChart;

                    // Don't load initial data - wait for strategy selection
                    // professionalChart.loadHistoricalData();

                    console.log('Professional TradingView chart initialized for Strategy Builder');
                } else {
                    console.log('Waiting for TradingView library...');
                    setTimeout(initProfessionalChart, 100);
                }
            };

            // Wait for DOM to be fully ready
            setTimeout(initProfessionalChart, 500);

            // Sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('strategy-sidebar');

            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('open');
                sidebarToggle.textContent = sidebar.classList.contains('open') ? 'Close Tools' : 'Strategy Tools';
            });

            // Header symbol change functionality
            document.getElementById('main-change-symbol').addEventListener('click', () => {
                const newSymbol = document.getElementById('main-symbol-input').value.trim().toUpperCase();
                if (newSymbol && professionalChart) {
                    professionalChart.changeSymbol(newSymbol);
                    document.getElementById('header-symbol').textContent = newSymbol;

                    // Update sidebar symbol input
                    document.getElementById('symbol').value = newSymbol;
                }
            });

            // Header timeframe buttons
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.timeframe-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    const interval = btn.dataset.interval;
                    if (professionalChart) {
                        professionalChart.changeInterval(interval);

                        // Update sidebar timeframe select
                        document.getElementById('timeframe').value = interval;
                    }
                });
            });

            // Sync sidebar controls with header
            document.getElementById('symbol').addEventListener('change', (e) => {
                document.getElementById('main-symbol-input').value = e.target.value;
            });

            document.getElementById('timeframe').addEventListener('change', (e) => {
                const interval = e.target.value;
                // Find and click the corresponding timeframe button
                const btn = document.querySelector(`[data-interval="${interval}"]`);
                if (btn) {
                    btn.click();
                }
            });

            // Connection status simulation (replace with real WebSocket status)
            setTimeout(() => {
                const dot = document.getElementById('main-connection-dot');
                const text = document.getElementById('main-connection-text');
                dot.classList.add('connected');
                text.textContent = 'Live Data';
            }, 3000);

            // Update header price display (replace with real data)
            const updateHeaderPrice = () => {
                const symbol = document.getElementById('header-symbol').textContent;
                if (symbol.includes('BTC')) {
                    const price = 45000 + (Math.random() - 0.5) * 1000;
                    const change = (Math.random() - 0.5) * 2;
                    document.getElementById('header-price').textContent = `$${price.toFixed(2)}`;
                    document.getElementById('header-change').textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                    document.getElementById('header-change').className = `price-change ${change >= 0 ? 'positive' : 'negative'}`;
                }
            };

            setInterval(updateHeaderPrice, 5000);
            updateHeaderPrice();

            // Initialize legacy components for backward compatibility
            if (window.IndicatorsManager) {
                window.indicatorsManager = new IndicatorsManager();
            }
            if (window.TradeManager) {
                window.tradeManager = new TradeManager();
            }
        });
    </script>
</body>
</html>

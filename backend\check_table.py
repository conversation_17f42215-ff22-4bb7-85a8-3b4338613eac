#!/usr/bin/env python3
"""
Check the actual database table structure
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.core.database import get_db_cursor
    
    print("Checking manual_marks table structure...")
    
    with get_db_cursor(dict_cursor=True) as cursor:
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'manual_marks'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("✅ manual_marks table exists")
            
            # Get table structure
            cursor.execute("DESCRIBE manual_marks")
            columns = cursor.fetchall()
            
            print(f"\nTable has {len(columns)} columns:")
            for col in columns:
                print(f"  - {col['Field']}: {col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}")
                
            # Check for our expected columns
            expected_columns = ['entry_timestamp', 'entry_price', 'side', 'quantity', 'status']
            existing_columns = [col['Field'] for col in columns]
            
            print(f"\nColumn check:")
            for col in expected_columns:
                status = "✅" if col in existing_columns else "❌"
                print(f"  {status} {col}")
                
        else:
            print("❌ manual_marks table does not exist")
            
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

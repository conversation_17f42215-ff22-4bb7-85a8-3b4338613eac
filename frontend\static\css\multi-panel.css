/* Multi-Panel Trading Chart Styles */

/* Trading Toolbar */
.trading-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: linear-gradient(135deg, #1e222d 0%, #2a2e39 100%);
    border: 1px solid #363c4e;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Symbol Selector */
.symbol-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.symbol-selector input {
    padding: 8px 12px;
    border: 1px solid #444;
    border-radius: 5px;
    background-color: #333;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    min-width: 150px;
    text-transform: uppercase;
}

.btn-icon {
    padding: 8px 12px;
    background: #2962ff;
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: #1e88e5;
    transform: translateY(-1px);
}

/* Timeframe Selector */
.timeframe-selector {
    display: flex;
    gap: 2px;
    background: #333;
    border-radius: 6px;
    padding: 2px;
}

.timeframe-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: #b2b5be;
    cursor: pointer;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.2s ease;
    min-width: 40px;
}

.timeframe-btn:hover {
    background: #444;
    color: #fff;
}

.timeframe-btn.active {
    background: #2962ff;
    color: white;
    box-shadow: 0 2px 4px rgba(41, 98, 255, 0.3);
}

/* Chart Tools */
.chart-tools {
    display: flex;
    gap: 2px;
    background: #333;
    border-radius: 6px;
    padding: 2px;
}

.tool-btn {
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: #b2b5be;
    cursor: pointer;
    border-radius: 4px;
    font-size: 16px;
    transition: all 0.2s ease;
    min-width: 40px;
}

.tool-btn:hover {
    background: #444;
    color: #fff;
}

.tool-btn.active {
    background: #2962ff;
    color: white;
}

/* View Controls */
.view-controls {
    display: flex;
    gap: 8px;
}

/* Multi-Panel Container */
.multi-panel-container {
    display: flex;
    flex-direction: column;
    gap: 2px;
    background: #131722;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

/* Chart Panel */
.chart-panel {
    background: #1e222d;
    border: 1px solid #363c4e;
    transition: all 0.3s ease;
}

.chart-panel:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.chart-panel:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* Main Panel - Larger height */
.main-panel .chart-content {
    height: 450px;
}

/* Volume Panel */
.volume-panel .chart-content {
    height: 120px;
}

/* Indicator Panels */
.indicator-panel .chart-content {
    height: 150px;
}

/* Panel Header */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: linear-gradient(135deg, #2a2e39 0%, #363c4e 100%);
    border-bottom: 1px solid #444;
    min-height: 40px;
}

.panel-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 14px;
    color: #d1d4dc;
}

.price-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.price-info .price {
    font-size: 16px;
    font-weight: 700;
    color: #fff;
}

.price-info .change {
    font-size: 14px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
}

.price-info .change.positive {
    color: #26a69a;
    background: rgba(38, 166, 154, 0.1);
}

.price-info .change.negative {
    color: #ef5350;
    background: rgba(239, 83, 80, 0.1);
}

/* Panel Controls */
.panel-controls {
    display: flex;
    gap: 4px;
}

.panel-settings,
.panel-toggle {
    padding: 4px 8px;
    border: none;
    background: transparent;
    color: #b2b5be;
    cursor: pointer;
    border-radius: 3px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.panel-settings:hover,
.panel-toggle:hover {
    background: #444;
    color: #fff;
}

/* Chart Content */
.chart-content {
    position: relative;
    width: 100%;
    background: #131722;
}

/* Chart Info Bar */
.chart-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #1e222d;
    border: 1px solid #363c4e;
    border-radius: 6px;
    margin-top: 10px;
    font-size: 12px;
    color: #b2b5be;
}

#chart-status {
    font-weight: 600;
}

#crosshair-info {
    font-family: 'Courier New', monospace;
}

#performance-info {
    color: #26a69a;
}

/* Collapsed Panel State */
.chart-panel.collapsed .chart-content {
    display: none;
}

.chart-panel.collapsed .panel-toggle::before {
    content: '+';
}

/* Responsive Design */
@media (max-width: 1200px) {
    .trading-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .toolbar-section {
        justify-content: center;
    }
    
    .main-panel .chart-content {
        height: 350px;
    }
    
    .volume-panel .chart-content {
        height: 100px;
    }
    
    .indicator-panel .chart-content {
        height: 120px;
    }
}

@media (max-width: 768px) {
    .timeframe-selector {
        flex-wrap: wrap;
    }
    
    .chart-tools {
        flex-wrap: wrap;
    }
    
    .main-panel .chart-content {
        height: 300px;
    }
    
    .volume-panel .chart-content {
        height: 80px;
    }
    
    .indicator-panel .chart-content {
        height: 100px;
    }
}

/* Loading Animation */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #b2b5be;
    font-size: 14px;
}

.chart-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #363c4e;
    border-top: 2px solid #2962ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

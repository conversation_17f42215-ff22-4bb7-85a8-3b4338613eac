// Main Application Logic

class StrategyBuilderApp {
    constructor() {
        this.currentData = null;
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Data fetching buttons
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchDataFromExchange();
        });
        
        document.getElementById('loadData').addEventListener('click', () => {
            this.loadDataFromDatabase();
        });

        document.getElementById('testChart').addEventListener('click', () => {
            this.testChart();
        });

        // Test connection on page load
        this.testConnections();
    }
    
    async testConnections() {
        try {
            // Test database connection
            const response = await fetch('/health');
            if (response.ok) {
                console.log('Backend connection successful');
            }
            
            // Test exchange connections
            const exchanges = ['binance', 'mexc'];
            for (const exchange of exchanges) {
                try {
                    const testResponse = await fetch(`/api/v1/ohlcv/test-connection?exchange=${exchange}`);
                    const result = await testResponse.json();
                    console.log(`${exchange} connection:`, result.success ? 'OK' : 'Failed');
                } catch (error) {
                    console.log(`${exchange} connection: Failed`);
                }
            }
        } catch (error) {
            console.error('Connection test failed:', error);
        }
    }
    
    async fetchDataFromExchange() {
        const symbol = document.getElementById('symbol').value.trim();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;
        const limit = parseInt(document.getElementById('limit').value);
        
        if (!symbol) {
            this.showError('Please enter a trading symbol');
            return;
        }
        
        try {
            this.showLoading('Fetching data from exchange...');
            
            const params = new URLSearchParams({
                symbol: symbol,
                timeframe: timeframe,
                exchange: exchange,
                limit: limit.toString()
            });
            
            const response = await fetch(`/api/v1/ohlcv/fetch?${params}`, {
                method: 'GET'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(`Fetched ${result.data.total_fetched} candles, stored ${result.data.new_records} new records`);
                
                // Load the data to display
                await this.loadDataFromDatabase();
                
            } else {
                throw new Error('Failed to fetch data from exchange');
            }
            
        } catch (error) {
            console.error('Error fetching data:', error);
            this.showError(`Error fetching data: ${error.message}`);
        }
    }
    
    async loadDataFromDatabase() {
        const symbol = document.getElementById('symbol').value.trim();
        const timeframe = document.getElementById('timeframe').value;
        const limit = parseInt(document.getElementById('limit').value);
        
        if (!symbol) {
            this.showError('Please enter a trading symbol');
            return;
        }
        
        try {
            this.showLoading('Loading data from database...');
            
            const params = new URLSearchParams({
                symbol: symbol,
                timeframe: timeframe,
                limit: limit.toString()
            });
            
            const response = await fetch(`/api/v1/ohlcv/data?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.currentData = result.data.ohlcv;
                
                // Update chart
                if (window.chartManager) {
                    window.chartManager.updateData(this.currentData);
                }

                // Update indicators manager
                if (window.indicatorsManager) {
                    window.indicatorsManager.setCurrentData(this.currentData);
                }
                
                this.showSuccess(`Loaded ${result.data.count} candles`);
                
            } else {
                throw new Error('No data found in database');
            }
            
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError(`Error loading data: ${error.message}`);
        }
    }
    
    showLoading(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="loading"></span> ${message}`;
        }
        
        // Disable buttons during loading
        this.setButtonsEnabled(false);
    }
    
    showSuccess(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="success">${message}</span>`;
        }
        
        // Re-enable buttons
        this.setButtonsEnabled(true);
        
        setTimeout(() => {
            if (statusElement.textContent.includes(message)) {
                statusElement.textContent = 'Ready';
            }
        }, 3000);
    }
    
    showError(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="error">${message}</span>`;
        }
        
        // Re-enable buttons
        this.setButtonsEnabled(true);
        
        setTimeout(() => {
            if (statusElement.textContent.includes(message)) {
                statusElement.textContent = 'Ready';
            }
        }, 5000);
    }
    
    setButtonsEnabled(enabled) {
        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.disabled = !enabled;
        });
    }
    
    getCurrentData() {
        return this.currentData;
    }

    testChart() {
        console.log('Testing chart with sample data...');

        // Generate sample OHLCV data
        const sampleData = [];
        const startTime = new Date('2024-01-01T00:00:00Z').getTime();
        const oneHour = 60 * 60 * 1000;

        for (let i = 0; i < 100; i++) {
            const timestamp = new Date(startTime + (i * oneHour)).toISOString();
            const open = 50000 + Math.random() * 10000;
            const close = open + (Math.random() - 0.5) * 2000;
            const high = Math.max(open, close) + Math.random() * 1000;
            const low = Math.min(open, close) - Math.random() * 1000;
            const volume = Math.random() * 1000000;

            sampleData.push({
                symbol: 'BTCUSDT',
                timeframe: '1h',
                timestamp: timestamp,
                open: open,
                high: high,
                low: low,
                close: close,
                volume: volume
            });
        }

        console.log('Generated sample data:', sampleData.slice(0, 3));

        // Update chart with sample data
        if (window.chartManager) {
            window.chartManager.updateData(sampleData);
        } else {
            console.error('Chart manager not available');
        }
    }
}

// Global app instance
let app = null;

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    app = new StrategyBuilderApp();

    // Initialize chart manager
    window.chartManager = new ChartManager('chart');
    console.log('Chart manager initialized:', window.chartManager);

    // Initialize indicators manager
    window.indicatorsManager = new IndicatorsManager();
    console.log('Indicators manager initialized:', window.indicatorsManager);

    // Initialize trade manager
    window.tradeManager = new TradeManager();
    console.log('Trade manager initialized:', window.tradeManager);
    
    // Add some helpful keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        // Ctrl+F to fetch data
        if (event.ctrlKey && event.key === 'f') {
            event.preventDefault();
            document.getElementById('fetchData').click();
        }
        
        // Ctrl+L to load data
        if (event.ctrlKey && event.key === 'l') {
            event.preventDefault();
            document.getElementById('loadData').click();
        }
        
        // Ctrl+I to calculate indicators
        if (event.ctrlKey && event.key === 'i') {
            event.preventDefault();
            document.getElementById('calculateIndicators').click();
        }
        
        // Escape to cancel marking mode
        if (event.key === 'Escape' && window.tradeManager) {
            window.tradeManager.stopMarking();
        }
    });
    
    console.log('Strategy Builder initialized successfully!');
    console.log('Keyboard shortcuts:');
    console.log('  Ctrl+F: Fetch data from exchange');
    console.log('  Ctrl+L: Load data from database');
    console.log('  Ctrl+I: Calculate indicators');
    console.log('  Escape: Cancel marking mode');
});

#!/usr/bin/env python3
"""
Test the running Strategy Builder system
"""
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        print("✅ Health check passed!")
        print(f"   Response: {response.json()}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_exchange_connection():
    """Test exchange connection"""
    print("\n🔍 Testing exchange connections...")
    
    # Test Binance
    response = requests.get(f"{BASE_URL}/api/v1/ohlcv/test-connection?exchange=binance")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ Binance connection successful!")
            rate_limit = data.get('rate_limit_status', {})
            print(f"   Rate limit: {rate_limit.get('remaining_requests', 'N/A')}/{rate_limit.get('max_requests', 'N/A')}")
        else:
            print("❌ Binance connection failed!")
            return False
    else:
        print(f"❌ Binance connection test failed: {response.status_code}")
        return False
    
    # Test MEXC
    response = requests.get(f"{BASE_URL}/api/v1/ohlcv/test-connection?exchange=mexc")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ MEXC connection successful!")
        else:
            print("⚠️  MEXC connection failed (this is expected if no API keys)")
    
    return True

def test_ohlcv_fetch():
    """Test OHLCV data fetching"""
    print("\n🔍 Testing OHLCV data fetching...")
    
    data = {
        'symbol': 'BTCUSDT',
        'timeframe': '1h',
        'exchange': 'binance',
        'limit': 5
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/ohlcv/fetch", json=data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ OHLCV fetch successful!")
            print(f"   Fetched: {result['data']['total_fetched']} candles")
            print(f"   Stored: {result['data']['new_records']} new records")
            return True
        else:
            print(f"❌ OHLCV fetch failed: {result.get('message', 'Unknown error')}")
            return False
    else:
        print(f"❌ OHLCV fetch failed: {response.status_code}")
        try:
            print(f"   Error: {response.json()}")
        except:
            print(f"   Error: {response.text}")
        return False

def test_indicators():
    """Test indicators endpoint"""
    print("\n🔍 Testing indicators...")
    
    # Test available indicators
    response = requests.get(f"{BASE_URL}/api/v1/indicators/available")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            indicators = data['data']['indicators']
            print(f"✅ Available indicators: {len(indicators)}")
            for indicator in indicators[:3]:  # Show first 3
                print(f"   - {indicator['name']}: {indicator['description']}")
            return True
        else:
            print("❌ Failed to get available indicators")
            return False
    else:
        print(f"❌ Indicators test failed: {response.status_code}")
        return False

def test_database():
    """Test database operations"""
    print("\n🔍 Testing database operations...")
    
    # Try to get OHLCV data (should work if we fetched some)
    response = requests.get(f"{BASE_URL}/api/v1/ohlcv/data?symbol=BTCUSDT&timeframe=1h&limit=5")
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ Database query successful!")
            print(f"   Retrieved: {data['data']['count']} records")
            return True
        else:
            print("⚠️  No data in database yet (this is normal for first run)")
            return True
    elif response.status_code == 404:
        print("⚠️  No data in database yet (this is normal for first run)")
        return True
    else:
        print(f"❌ Database test failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Strategy Builder System Test")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health),
        ("Exchange Connections", test_exchange_connection),
        ("OHLCV Data Fetch", test_ohlcv_fetch),
        ("Indicators", test_indicators),
        ("Database Operations", test_database)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # Small delay between tests
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Strategy Builder is running successfully!")
        print("\n🌐 Access the system at:")
        print(f"   - API Documentation: {BASE_URL}/docs")
        print(f"   - Health Check: {BASE_URL}/health")
        print(f"   - Main Interface: {BASE_URL}/")
    else:
        print(f"⚠️  {total - passed} test(s) failed. Check the logs above.")
    
    return passed == total

if __name__ == "__main__":
    main()

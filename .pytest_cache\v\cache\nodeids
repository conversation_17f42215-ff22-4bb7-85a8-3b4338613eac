["tests/unit/test_validators.py::TestDateTimeValidator::test_invalid_datetime_range", "tests/unit/test_validators.py::TestDateTimeValidator::test_none_values", "tests/unit/test_validators.py::TestDateTimeValidator::test_valid_datetime_range", "tests/unit/test_validators.py::TestExchangeValidator::test_invalid_exchange", "tests/unit/test_validators.py::TestExchangeValidator::test_valid_exchanges[BINANCE]", "tests/unit/test_validators.py::TestExchangeValidator::test_valid_exchanges[MEXC]", "tests/unit/test_validators.py::TestExchangeValidator::test_valid_exchanges[binance]", "tests/unit/test_validators.py::TestExchangeValidator::test_valid_exchanges[mexc]", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_invalid_bollinger_bands_config", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_invalid_ema_period", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_invalid_macd_config", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_invalid_rsi_period", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_valid_bollinger_bands_config", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_valid_ema_config", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_valid_macd_config", "tests/unit/test_validators.py::TestIndicatorsConfigValidator::test_valid_rsi_config", "tests/unit/test_validators.py::TestLimitValidator::test_invalid_limits[-1]", "tests/unit/test_validators.py::TestLimitValidator::test_invalid_limits[0]", "tests/unit/test_validators.py::TestLimitValidator::test_invalid_limits[2001]", "tests/unit/test_validators.py::TestLimitValidator::test_invalid_limits[5000]", "tests/unit/test_validators.py::TestLimitValidator::test_valid_limits[1000]", "tests/unit/test_validators.py::TestLimitValidator::test_valid_limits[100]", "tests/unit/test_validators.py::TestLimitValidator::test_valid_limits[1]", "tests/unit/test_validators.py::TestLimitValidator::test_valid_limits[2000]", "tests/unit/test_validators.py::TestLimitValidator::test_valid_limits[500]", "tests/unit/test_validators.py::TestSymbolValidator::test_empty_symbol", "tests/unit/test_validators.py::TestSymbolValidator::test_invalid_characters", "tests/unit/test_validators.py::TestSymbolValidator::test_symbol_case_insensitive", "tests/unit/test_validators.py::TestSymbolValidator::test_symbol_with_whitespace", "tests/unit/test_validators.py::TestSymbolValidator::test_valid_symbol", "tests/unit/test_validators.py::TestSymbolValidator::test_whitespace_only_symbol", "tests/unit/test_validators.py::TestTimeframeValidator::test_invalid_timeframe", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[12h]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[15m]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[1M]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[1d]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[1h]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[1m]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[1w]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[2h]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[30m]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[3d]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[3m]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[4h]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[5m]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[6h]", "tests/unit/test_validators.py::TestTimeframeValidator::test_valid_timeframes[8h]", "tests/unit/test_validators.py::TestTradeMarkRequest::test_entry_mark_without_side", "tests/unit/test_validators.py::TestTradeMarkRequest::test_invalid_entry_side", "tests/unit/test_validators.py::TestTradeMarkRequest::test_invalid_mark_type", "tests/unit/test_validators.py::TestTradeMarkRequest::test_negative_price", "tests/unit/test_validators.py::TestTradeMarkRequest::test_valid_entry_mark", "tests/unit/test_validators.py::TestTradeMarkRequest::test_valid_exit_mark"]
"""
API endpoints for manual marking functionality
"""

from fastapi import APIRouter, HTTPException
from typing import List, Optional
from datetime import datetime
import json
import csv
import io
from fastapi.responses import StreamingResponse

from app.core.database import get_db_cursor, DatabaseError
from app.schemas.marks import MarkEntry, MarkExit, MarkResponse, MarkListResponse

router = APIRouter()


@router.post("/entry", response_model=MarkResponse)
async def create_entry_mark(mark_data: MarkEntry):
    """Create a new entry mark"""
    try:
        # Debug logging
        print(f"Received mark_data: {mark_data}")
        print(f"mark_data.side: {mark_data.side}")
        print(f"mark_data.side type: {type(mark_data.side)}")

        # Validate side field
        if not mark_data.side:
            raise HTTPException(status_code=400, detail="Side field is required")

        # Ensure side is uppercase
        entry_side = mark_data.side.upper() if mark_data.side else None
        if entry_side not in ['BUY', 'SELL']:
            raise HTTPException(status_code=400, detail="Side must be 'buy' or 'sell'")

        entry_timestamp = datetime.fromtimestamp(mark_data.timestamp)
        entry_ohlcv_json = json.dumps(mark_data.ohlcv_data) if mark_data.ohlcv_data else None
        entry_indicator_json = json.dumps(mark_data.indicator_data) if mark_data.indicator_data else None

        with get_db_cursor(dict_cursor=True) as cursor:
            # Insert new manual mark using the correct schema
            insert_sql = """
                INSERT INTO manual_marks (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    indicator_snapshot, ohlcv_snapshot, linked_trade_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_sql, (
                'BTCUSDT',  # Default symbol - should be passed from frontend
                '15m',      # Default timeframe - should be passed from frontend
                'ENTRY',    # Mark type
                entry_side,  # Entry side (BUY/SELL) - now safely handled
                entry_timestamp,
                mark_data.price,
                entry_indicator_json,
                entry_ohlcv_json,
                None        # linked_trade_id
            ))

            mark_id = cursor.lastrowid

        return MarkResponse(
            success=True,
            message="Entry mark created successfully",
            data={
                "id": mark_id,
                "symbol": "BTCUSDT",
                "timeframe": "15m",
                "mark_type": "ENTRY",
                "entry_side": entry_side,
                "timestamp": mark_data.timestamp,
                "price": mark_data.price,
                "indicator_snapshot": mark_data.indicator_data,
                "ohlcv_snapshot": mark_data.ohlcv_data
            }
        )

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating entry mark: {str(e)}")


@router.post("/exit", response_model=MarkResponse)
async def create_exit_mark(exit_data: MarkExit):
    """Add exit to an existing entry mark"""
    try:
        exit_timestamp = datetime.fromtimestamp(exit_data.timestamp)
        exit_ohlcv_json = json.dumps(exit_data.ohlcv_data) if exit_data.ohlcv_data else None
        exit_indicator_json = json.dumps(exit_data.indicator_data) if exit_data.indicator_data else None

        with get_db_cursor(dict_cursor=True) as cursor:

            # Create a new exit mark instead of updating (since schema doesn't support updates)
            insert_sql = """
                INSERT INTO manual_marks (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    indicator_snapshot, ohlcv_snapshot, linked_trade_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor.execute(insert_sql, (
                'BTCUSDT',  # Default symbol
                '15m',      # Default timeframe
                'EXIT',     # Mark type
                None,       # Entry side not applicable for exit
                exit_timestamp,
                exit_data.price,
                exit_indicator_json,
                exit_ohlcv_json,
                exit_data.entry_id  # Link to entry mark
            ))

            mark_id = cursor.lastrowid

        return MarkResponse(
            success=True,
            message="Exit mark created successfully",
            data={
                "id": mark_id,
                "symbol": "BTCUSDT",
                "timeframe": "15m",
                "mark_type": "EXIT",
                "entry_side": None,
                "timestamp": exit_data.timestamp,
                "price": exit_data.price,
                "indicator_snapshot": exit_data.indicator_data,
                "ohlcv_snapshot": exit_data.ohlcv_data,
                "linked_trade_id": exit_data.entry_id
            }
        )

    except HTTPException:
        raise
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating exit mark: {str(e)}")


@router.get("", response_model=MarkListResponse)
async def get_marks(status: Optional[str] = None, limit: int = 100):
    """Get all marks with optional filtering"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            # Build query
            base_sql = "SELECT * FROM manual_marks"
            params = []

            if status:
                base_sql += " WHERE status = %s"
                params.append(status)

            base_sql += " ORDER BY created_at DESC LIMIT %s"
            params.append(limit)

            cursor.execute(base_sql, params)
            marks = cursor.fetchall()

        marks_data = []
        for mark in marks:
            mark_dict = {
                "id": mark['id'],
                "entry_timestamp": int(mark['entry_timestamp'].timestamp()),
                "entry_price": float(mark['entry_price']),
                "side": mark['side'],
                "quantity": float(mark['quantity']),
                "notes": mark['notes'],
                "status": mark['status'],
                "pnl": float(mark['pnl']) if mark['pnl'] else 0,
                "created_at": mark['created_at'].isoformat(),
                "updated_at": mark['updated_at'].isoformat()
            }

            if mark['exit_timestamp']:
                mark_dict["exit_timestamp"] = int(mark['exit_timestamp'].timestamp())
                mark_dict["exit_price"] = float(mark['exit_price'])
                mark_dict["exit_quantity"] = float(mark['exit_quantity']) if mark['exit_quantity'] else 0
                mark_dict["exit_notes"] = mark['exit_notes']

            marks_data.append(mark_dict)

        return MarkListResponse(
            success=True,
            message=f"Retrieved {len(marks_data)} marks",
            data=marks_data
        )

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving marks: {str(e)}")


@router.delete("/{mark_id}", response_model=MarkResponse)
async def delete_mark(mark_id: int):
    """Delete a specific mark"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            # Check if mark exists
            cursor.execute("SELECT id FROM manual_marks WHERE id = %s", (mark_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="Mark not found")

            # Delete the mark
            cursor.execute("DELETE FROM manual_marks WHERE id = %s", (mark_id,))

        return MarkResponse(
            success=True,
            message="Mark deleted successfully",
            data={"id": mark_id}
        )

    except HTTPException:
        raise
    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting mark: {str(e)}")


@router.delete("", response_model=MarkResponse)
async def clear_all_marks():
    """Clear all marks"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            # Get count before deletion
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            result = cursor.fetchone()
            deleted_count = result['count'] if result else 0

            # Delete all marks
            cursor.execute("DELETE FROM manual_marks")

        return MarkResponse(
            success=True,
            message=f"Cleared {deleted_count} marks",
            data={"deleted_count": deleted_count}
        )

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error clearing marks: {str(e)}")


@router.get("/export")
async def export_marks():
    """Export marks as CSV"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            cursor.execute("SELECT * FROM manual_marks ORDER BY created_at DESC")
            marks = cursor.fetchall()

        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'ID', 'Entry Time', 'Entry Price', 'Side', 'Quantity',
            'Exit Time', 'Exit Price', 'Exit Quantity', 'P&L',
            'Status', 'Entry Notes', 'Exit Notes', 'Created At'
        ])

        # Write data
        for mark in marks:
            writer.writerow([
                mark['id'],
                mark['entry_timestamp'].isoformat() if mark['entry_timestamp'] else '',
                mark['entry_price'],
                mark['side'],
                mark['quantity'],
                mark['exit_timestamp'].isoformat() if mark['exit_timestamp'] else '',
                mark['exit_price'] if mark['exit_price'] else '',
                mark['exit_quantity'] if mark['exit_quantity'] else '',
                mark['pnl'] if mark['pnl'] else 0,
                mark['status'],
                mark['notes'] if mark['notes'] else '',
                mark['exit_notes'] if mark['exit_notes'] else '',
                mark['created_at'].isoformat()
            ])

        output.seek(0)

        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=marks_export.csv"}
        )

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting marks: {str(e)}")


@router.get("/statistics")
async def get_statistics():
    """Get marking statistics"""
    try:
        with get_db_cursor(dict_cursor=True) as cursor:
            # Get total marks
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            total_marks = cursor.fetchone()['count']

            # Get open marks
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks WHERE status = 'open'")
            open_marks = cursor.fetchone()['count']

            # Get closed marks
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks WHERE status = 'closed'")
            closed_marks = cursor.fetchone()['count']

            # Get winning trades
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks WHERE status = 'closed' AND pnl > 0")
            winning_trades = cursor.fetchone()['count']

            # Calculate win rate
            win_rate = (winning_trades / closed_marks * 100) if closed_marks > 0 else 0

            # Calculate total P&L
            cursor.execute("SELECT SUM(pnl) as total_pnl FROM manual_marks WHERE status = 'closed' AND pnl IS NOT NULL")
            result = cursor.fetchone()
            total_pnl = float(result['total_pnl']) if result['total_pnl'] else 0

        return {
            "success": True,
            "data": {
                "total_marks": total_marks,
                "open_positions": open_marks,
                "closed_trades": closed_marks,
                "win_rate": round(win_rate, 2),
                "total_pnl": round(total_pnl, 2),
                "winning_trades": winning_trades,
                "losing_trades": closed_marks - winning_trades
            }
        }

    except DatabaseError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting statistics: {str(e)}")

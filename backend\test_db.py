#!/usr/bin/env python3
"""
Test database connection and create tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import create_tables, get_db_cursor

def test_database():
    try:
        print("Testing database connection...")
        
        # Create tables
        print("Creating tables...")
        create_tables()
        print("✅ Tables created successfully")
        
        # Test connection
        print("Testing database connection...")
        with get_db_cursor(dict_cursor=True) as cursor:
            cursor.execute("SHOW TABLES LIKE 'manual_marks'")
            result = cursor.fetchone()
            if result:
                print("✅ manual_marks table exists")
            else:
                print("❌ manual_marks table does not exist")
                
            # Check table structure
            cursor.execute("DESCRIBE manual_marks")
            columns = cursor.fetchall()
            print(f"✅ Table has {len(columns)} columns")
            for col in columns:
                print(f"  - {col['Field']}: {col['Type']}")
                
        print("✅ Database test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

if __name__ == "__main__":
    test_database()

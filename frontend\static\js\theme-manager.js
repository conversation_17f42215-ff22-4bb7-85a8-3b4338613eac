/**
 * Chart Theme Manager
 * Handles light/dark theme switching for the TradingView chart
 */

class ChartThemeManager {
    constructor() {
        this.currentTheme = 'dark'; // Default theme
        this.themes = {
            dark: {
                background: '#1e222d',
                grid: '#363c4e',
                text: '#d1d4dc',
                crosshair: '#758696',
                borderColor: '#4a5568',
                upColor: '#26a69a',
                downColor: '#ef5350',
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350'
            },
            light: {
                background: '#ffffff',
                grid: '#e1e5e9',
                text: '#2e3338',
                crosshair: '#9598a1',
                borderColor: '#d1d4dc',
                upColor: '#089981',
                downColor: '#f23645',
                wickUpColor: '#089981',
                wickDownColor: '#f23645'
            }
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSavedTheme();
    }

    setupEventListeners() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeLabel = document.getElementById('theme-label');
        
        if (themeToggle && themeLabel) {
            themeToggle.addEventListener('change', (e) => {
                const newTheme = e.target.checked ? 'dark' : 'light';
                this.switchTheme(newTheme);
                this.updateThemeLabel(newTheme);
                this.saveTheme(newTheme);
            });
        }
    }

    switchTheme(themeName) {
        if (!this.themes[themeName]) {
            console.error('Unknown theme:', themeName);
            return;
        }

        this.currentTheme = themeName;
        const theme = this.themes[themeName];

        // Update chart theme if chart exists
        if (window.professionalChart && window.professionalChart.chart) {
            this.applyChartTheme(theme);
        }

        // Store theme for when chart is created
        this.pendingTheme = theme;
        
        console.log(`Switched to ${themeName} theme`);
    }

    applyChartTheme(theme) {
        try {
            // Use the chart's built-in theme update method if available
            if (window.professionalChart && typeof window.professionalChart.updateTheme === 'function') {
                window.professionalChart.updateTheme(theme);
            } else if (window.professionalChart && window.professionalChart.chart) {
                // Fallback to direct chart manipulation
                const chart = window.professionalChart.chart;

                chart.applyOptions({
                    layout: {
                        background: {
                            type: 'solid',
                            color: theme.background
                        },
                        textColor: theme.text,
                    },
                    grid: {
                        vertLines: {
                            color: theme.grid,
                            style: 1,
                            visible: true,
                        },
                        horzLines: {
                            color: theme.grid,
                            style: 1,
                            visible: true,
                        },
                    },
                    crosshair: {
                        mode: 0,
                        vertLine: {
                            color: theme.crosshair,
                            width: 1,
                            style: 3,
                        },
                        horzLine: {
                            color: theme.crosshair,
                            width: 1,
                            style: 3,
                        },
                    },
                    timeScale: {
                        borderColor: theme.borderColor,
                        textColor: theme.text,
                    },
                    rightPriceScale: {
                        borderColor: theme.borderColor,
                        textColor: theme.text,
                    },
                });

                // Update candlestick series colors
                if (window.professionalChart.candlestickSeries) {
                    window.professionalChart.candlestickSeries.applyOptions({
                        upColor: theme.upColor,
                        downColor: theme.downColor,
                        borderUpColor: theme.upColor,
                        borderDownColor: theme.downColor,
                        wickUpColor: theme.wickUpColor,
                        wickDownColor: theme.wickDownColor,
                    });
                }
            }

            console.log('Chart theme applied successfully');

        } catch (error) {
            console.error('Error applying chart theme:', error);
        }
    }

    updateVolumeColors(theme) {
        // Volume colors are typically set per data point
        // This will be handled when volume data is loaded
        console.log('Volume colors updated for theme:', this.currentTheme);
    }

    updateThemeLabel(themeName) {
        const themeLabel = document.getElementById('theme-label');
        if (themeLabel) {
            themeLabel.textContent = themeName === 'dark' ? 'Dark Theme' : 'Light Theme';
        }
    }

    loadSavedTheme() {
        try {
            const savedTheme = localStorage.getItem('chart-theme');
            if (savedTheme && this.themes[savedTheme]) {
                this.currentTheme = savedTheme;
                
                // Update toggle state
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    themeToggle.checked = savedTheme === 'dark';
                }
                
                this.updateThemeLabel(savedTheme);
                
                // Apply theme if chart exists, otherwise store for later
                if (window.professionalChart && window.professionalChart.chart) {
                    this.applyChartTheme(this.themes[savedTheme]);
                } else {
                    this.pendingTheme = this.themes[savedTheme];
                }
                
                console.log('Loaded saved theme:', savedTheme);
            }
        } catch (error) {
            console.error('Error loading saved theme:', error);
        }
    }

    saveTheme(themeName) {
        try {
            localStorage.setItem('chart-theme', themeName);
            console.log('Theme saved:', themeName);
        } catch (error) {
            console.error('Error saving theme:', error);
        }
    }

    // Method to be called when chart is initialized
    initializeChartTheme() {
        if (this.pendingTheme) {
            this.applyChartTheme(this.pendingTheme);
            this.pendingTheme = null;
        } else {
            this.applyChartTheme(this.themes[this.currentTheme]);
        }
    }

    // Get current theme for other components
    getCurrentTheme() {
        return {
            name: this.currentTheme,
            colors: this.themes[this.currentTheme]
        };
    }

    // Method to get volume colors for data loading
    getVolumeColors() {
        const theme = this.themes[this.currentTheme];
        return {
            up: theme.upColor + '80', // 50% opacity
            down: theme.downColor + '80' // 50% opacity
        };
    }

    // Method for indicators to get theme-appropriate colors
    getIndicatorColors() {
        const theme = this.themes[this.currentTheme];
        if (this.currentTheme === 'light') {
            return {
                ema: ['#d32f2f', '#1976d2', '#7b1fa2'], // Darker colors for light theme
                bollinger: '#7b1fa2',
                macd: '#1976d2',
                rsi: '#d32f2f'
            };
        } else {
            return {
                ema: ['#FF6B6B', '#4ECDC4', '#45B7D1'], // Original colors for dark theme
                bollinger: '#9C27B0',
                macd: '#2196F3',
                rsi: '#E91E63'
            };
        }
    }
}

// Global theme manager instance
window.chartThemeManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chartThemeManager = new ChartThemeManager();
    console.log('Chart Theme Manager initialized');
});

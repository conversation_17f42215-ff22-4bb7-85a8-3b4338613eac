/**
 * Marking Tools for TradingView Chart
 * Handles entry/exit marking with database integration
 */

class MarkingTools {
    constructor(chart) {
        this.chart = chart;
        this.marks = new Map(); // Store marks by ID
        this.currentClickData = null;
        this.isMarkingMode = false;
        
        this.initializeEventListeners();
        this.loadExistingMarks();
    }

    initializeEventListeners() {
        // Chart click handlers
        if (this.chart && this.chart.chart && this.chart.container) {
            console.log('Setting up chart click handlers for marking tools');

            // Left click for entry
            this.chart.container.addEventListener('click', (e) => {
                if (this.isMarkingMode) {
                    console.log('Chart clicked for entry marking');
                    this.handleChartClick(e, 'entry');
                }
            });

            // Right click for exit
            this.chart.container.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                if (this.isMarkingMode) {
                    console.log('Chart right-clicked for exit marking');
                    this.handleChartClick(e, 'exit');
                }
            });
        } else {
            console.warn('Chart not ready for marking tools event listeners');
        }

        // Modal event listeners
        this.initializeModalListeners();

        // Sidebar event listeners
        this.initializeSidebarListeners();
    }

    initializeModalListeners() {
        // Entry modal
        const entryModal = document.getElementById('entry-modal');
        const entryClose = document.getElementById('entry-modal-close');
        const cancelEntry = document.getElementById('cancel-entry');
        const confirmEntry = document.getElementById('confirm-entry');

        entryClose.addEventListener('click', () => this.closeModal('entry-modal'));
        cancelEntry.addEventListener('click', () => this.closeModal('entry-modal'));
        confirmEntry.addEventListener('click', () => this.confirmEntry());

        // Exit modal
        const exitModal = document.getElementById('exit-modal');
        const exitClose = document.getElementById('exit-modal-close');
        const cancelExit = document.getElementById('cancel-exit');
        const confirmExit = document.getElementById('confirm-exit');

        exitClose.addEventListener('click', () => this.closeModal('exit-modal'));
        cancelExit.addEventListener('click', () => this.closeModal('exit-modal'));
        confirmExit.addEventListener('click', () => this.confirmExit());

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    initializeSidebarListeners() {
        // Export marks
        document.getElementById('export-marks').addEventListener('click', () => {
            this.exportMarks();
        });

        // Clear all marks
        document.getElementById('clear-all-marks').addEventListener('click', () => {
            this.clearAllMarks();
        });
    }

    handleChartClick(event, type) {
        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Get time and price from coordinates
        let time, price;

        try {
            time = this.chart.chart.timeScale().coordinateToTime(x);
            price = this.chart.chart.priceScale('right').coordinateToPrice(y);
        } catch (error) {
            console.warn('Could not get time/price from coordinates:', error);
            // Fallback: use current time and a reasonable price
            time = Math.floor(Date.now() / 1000);
            price = 50000; // Default price
        }

        if (!time || !price) {
            console.warn('Could not get time/price from coordinates');
            return;
        }

        this.currentClickData = {
            time: time,
            price: price,
            x: x,
            y: y
        };

        if (type === 'entry') {
            this.showEntryModal();
        } else if (type === 'exit') {
            this.showExitModal();
        }
    }

    showEntryModal() {
        const modal = document.getElementById('entry-modal');
        const timeElement = document.getElementById('entry-time');
        const priceElement = document.getElementById('entry-price');

        // Format time and price
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        timeElement.textContent = formattedTime;
        priceElement.textContent = formattedPrice;

        // Reset form
        document.getElementById('entry-side').value = 'buy';
        document.getElementById('entry-quantity').value = '';
        document.getElementById('entry-notes').value = '';

        modal.style.display = 'block';
    }

    showExitModal() {
        const modal = document.getElementById('exit-modal');
        const timeElement = document.getElementById('exit-time');
        const priceElement = document.getElementById('exit-price');
        const entrySelect = document.getElementById('exit-entry-select');

        // Format time and price
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        timeElement.textContent = formattedTime;
        priceElement.textContent = formattedPrice;

        // Populate open entries
        this.populateOpenEntries(entrySelect);

        // Reset form
        document.getElementById('exit-quantity').value = '';
        document.getElementById('exit-notes').value = '';

        modal.style.display = 'block';
    }

    populateOpenEntries(selectElement) {
        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select an open entry...</option>';

        // Add open entries
        this.marks.forEach((mark, id) => {
            if (mark.status === 'open') {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)} (${mark.quantity})`;
                selectElement.appendChild(option);
            }
        });
    }

    async confirmEntry() {
        const side = document.getElementById('entry-side').value;
        const quantity = parseFloat(document.getElementById('entry-quantity').value);
        const notes = document.getElementById('entry-notes').value;

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        const entryData = {
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            side: side,
            quantity: quantity,
            notes: notes,
            ohlcv_data: await this.getOHLCVData(this.currentClickData.time),
            indicator_data: await this.getIndicatorData(this.currentClickData.time)
        };

        try {
            const response = await fetch('/api/v1/marks/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryData)
            });

            const result = await response.json();
            
            if (result.success) {
                // Add mark to chart
                this.addMarkToChart(result.data);
                
                // Update sidebar
                this.updateSidebar();
                
                this.closeModal('entry-modal');
                console.log('Entry mark added successfully');
            } else {
                alert('Error adding entry mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding entry mark:', error);
            alert('Error adding entry mark');
        }
    }

    async confirmExit() {
        const entryId = document.getElementById('exit-entry-select').value;
        const quantity = parseFloat(document.getElementById('exit-quantity').value);
        const notes = document.getElementById('exit-notes').value;

        if (!entryId) {
            alert('Please select an entry to exit');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid exit quantity');
            return;
        }

        const exitData = {
            entry_id: entryId,
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            quantity: quantity,
            notes: notes,
            ohlcv_data: await this.getOHLCVData(this.currentClickData.time),
            indicator_data: await this.getIndicatorData(this.currentClickData.time)
        };

        try {
            const response = await fetch('/api/v1/marks/exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(exitData)
            });

            const result = await response.json();
            
            if (result.success) {
                // Update mark on chart
                this.updateMarkOnChart(result.data);
                
                // Update sidebar
                this.updateSidebar();
                
                this.closeModal('exit-modal');
                console.log('Exit mark added successfully');
            } else {
                alert('Error adding exit mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding exit mark:', error);
            alert('Error adding exit mark');
        }
    }

    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        this.currentClickData = null;
    }

    async getOHLCVData(timestamp) {
        // Get OHLCV data from the chart at the specific timestamp
        if (!this.chart.currentData) return null;

        const candle = this.chart.currentData.find(c => c.time === timestamp);
        return candle || null;
    }

    async getIndicatorData(timestamp) {
        // Get indicator data if available
        const indicators = {};
        
        // Add EMA data if available
        if (window.indicatorsManager && window.indicatorsManager.indicatorSeries) {
            const series = window.indicatorsManager.indicatorSeries;
            Object.keys(series).forEach(key => {
                // This would need to be implemented based on how indicators store their data
                indicators[key] = null; // Placeholder
            });
        }

        return indicators;
    }

    addMarkToChart(markData) {
        // Store mark data
        this.marks.set(markData.id, markData);

        // Add visual marker to chart
        const color = markData.side === 'buy' ? '#4caf50' : '#f44336';
        
        const marker = {
            time: markData.entry_timestamp,
            position: 'belowBar',
            color: color,
            shape: 'circle',
            text: `${markData.side.toUpperCase()} @ $${markData.entry_price.toFixed(2)}`,
            size: 2
        };

        // Add marker to candlestick series
        if (this.chart.candlestickSeries) {
            const existingMarkers = this.chart.candlestickSeries.markers() || [];
            this.chart.candlestickSeries.setMarkers([...existingMarkers, marker]);
        }
    }

    updateMarkOnChart(markData) {
        // Update stored mark data
        this.marks.set(markData.id, markData);

        // Update visual representation if needed
        this.refreshChartMarkers();
    }

    refreshChartMarkers() {
        if (!this.chart.candlestickSeries) return;

        const markers = [];
        this.marks.forEach(mark => {
            const color = mark.side === 'buy' ? '#4caf50' : '#f44336';
            
            // Entry marker
            markers.push({
                time: mark.entry_timestamp,
                position: 'belowBar',
                color: color,
                shape: 'circle',
                text: `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)}`,
                size: 2
            });

            // Exit marker if exists
            if (mark.exit_timestamp) {
                markers.push({
                    time: mark.exit_timestamp,
                    position: 'aboveBar',
                    color: color,
                    shape: 'square',
                    text: `EXIT @ $${mark.exit_price.toFixed(2)}`,
                    size: 2
                });
            }
        });

        this.chart.candlestickSeries.setMarkers(markers);
    }

    async loadExistingMarks() {
        try {
            const response = await fetch('/api/v1/marks');
            const result = await response.json();
            
            if (result.success && result.data) {
                result.data.forEach(mark => {
                    this.marks.set(mark.id, mark);
                });
                
                this.refreshChartMarkers();
                this.updateSidebar();
            }
        } catch (error) {
            console.error('Error loading existing marks:', error);
        }
    }

    updateSidebar() {
        this.updateMarksList();
        this.updateStatistics();
    }

    updateMarksList() {
        const container = document.getElementById('active-marks-list');
        
        if (this.marks.size === 0) {
            container.innerHTML = '<div class="no-marks">No active marks</div>';
            return;
        }

        let html = '';
        this.marks.forEach(mark => {
            const pnl = mark.exit_price ? 
                ((mark.exit_price - mark.entry_price) * (mark.side === 'buy' ? 1 : -1) * mark.quantity).toFixed(2) : 
                'Open';
            
            html += `
                <div class="mark-item">
                    <div class="mark-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                            <span class="mark-side ${mark.side}">${mark.side}</span>
                            <span>$${mark.entry_price.toFixed(2)}</span>
                            <span style="font-size: 11px; color: #888;">${mark.quantity}</span>
                        </div>
                        <div style="font-size: 11px; color: ${pnl === 'Open' ? '#888' : (parseFloat(pnl) >= 0 ? '#4caf50' : '#f44336')};">
                            P&L: ${pnl === 'Open' ? pnl : '$' + pnl}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    updateStatistics() {
        const totalEntries = this.marks.size;
        const openPositions = Array.from(this.marks.values()).filter(m => m.status === 'open').length;
        const closedTrades = totalEntries - openPositions;
        
        const closedMarks = Array.from(this.marks.values()).filter(m => m.status === 'closed');
        const winningTrades = closedMarks.filter(m => {
            const pnl = (m.exit_price - m.entry_price) * (m.side === 'buy' ? 1 : -1);
            return pnl > 0;
        }).length;
        
        const winRate = closedTrades > 0 ? ((winningTrades / closedTrades) * 100).toFixed(1) : 0;

        document.getElementById('total-entries').textContent = totalEntries;
        document.getElementById('open-positions').textContent = openPositions;
        document.getElementById('closed-trades').textContent = closedTrades;
        document.getElementById('win-rate').textContent = winRate + '%';
    }

    enableMarkingMode() {
        this.isMarkingMode = true;
        console.log('Marking mode enabled - Click chart to add entries, right-click to add exits');

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Marking mode active - Click to add entries, right-click for exits';
            statusElement.className = 'status warning';
        }
    }

    disableMarkingMode() {
        this.isMarkingMode = false;
        console.log('Marking mode disabled');

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Chart ready';
            statusElement.className = 'status info';
        }
    }

    async exportMarks() {
        try {
            const response = await fetch('/api/v1/marks/export');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `marks_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting marks:', error);
            alert('Error exporting marks');
        }
    }

    async clearAllMarks() {
        if (!confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/v1/marks', {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.marks.clear();
                this.refreshChartMarkers();
                this.updateSidebar();
                console.log('All marks cleared');
            } else {
                alert('Error clearing marks: ' + result.message);
            }
        } catch (error) {
            console.error('Error clearing marks:', error);
            alert('Error clearing marks');
        }
    }
}

// Global marking tools instance
let markingTools = null;

// Initialize when chart is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for chart to be initialized
    const initMarkingTools = () => {
        if (window.tradingViewChart) {
            markingTools = new MarkingTools(window.tradingViewChart);
            window.markingTools = markingTools;
            console.log('Marking tools initialized');
        } else {
            setTimeout(initMarkingTools, 500);
        }
    };
    
    setTimeout(initMarkingTools, 1000);
});

/**
 * Marking Tools for TradingView Chart
 * Handles entry/exit marking with database integration
 */

class MarkingTools {
    constructor(chart) {
        this.chart = chart;
        this.marks = new Map(); // Store marks by ID
        this.currentClickData = null;
        this.isMarkingMode = false;
        
        this.initializeEventListeners();
        this.loadExistingMarks();
    }

    initializeEventListeners() {
        // Chart click handlers using TradingView's built-in events
        if (this.chart && this.chart.chart) {
            console.log('Setting up chart click handlers for marking tools');

            // Use TradingView's click event
            this.chart.chart.subscribeClick((param) => {
                console.log('Chart clicked via TradingView API, marking mode:', this.isMarkingMode);
                console.log('Click param:', param);

                if (this.isMarkingMode && param.point) {
                    console.log('Processing chart click for entry marking');
                    this.handleTradingViewClick(param, 'entry');
                }
            });

            // For right-click, we still need to use DOM events on the container
            if (this.chart.container) {
                this.chart.container.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    console.log('Chart container right-clicked, marking mode:', this.isMarkingMode);
                    if (this.isMarkingMode) {
                        console.log('Chart right-clicked for exit marking');
                        this.handleChartClick(e, 'exit');
                    }
                });
            }

            console.log('Chart click handlers set up successfully');
        } else {
            console.warn('Chart not ready for marking tools event listeners');
            console.log('Chart:', this.chart);
            console.log('Chart.chart:', this.chart?.chart);
            console.log('Chart.container:', this.chart?.container);
        }

        // Modal event listeners
        this.initializeModalListeners();

        // Sidebar event listeners
        this.initializeSidebarListeners();
    }

    initializeModalListeners() {
        // Entry modal
        const entryModal = document.getElementById('entry-modal');
        const entryClose = document.getElementById('entry-modal-close');
        const cancelEntry = document.getElementById('cancel-entry');
        const confirmEntry = document.getElementById('confirm-entry');

        entryClose.addEventListener('click', () => this.closeModal('entry-modal'));
        cancelEntry.addEventListener('click', () => this.closeModal('entry-modal'));
        confirmEntry.addEventListener('click', () => this.confirmEntry());

        // Exit modal
        const exitModal = document.getElementById('exit-modal');
        const exitClose = document.getElementById('exit-modal-close');
        const cancelExit = document.getElementById('cancel-exit');
        const confirmExit = document.getElementById('confirm-exit');

        exitClose.addEventListener('click', () => this.closeModal('exit-modal'));
        cancelExit.addEventListener('click', () => this.closeModal('exit-modal'));
        confirmExit.addEventListener('click', () => this.confirmExit());

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    initializeSidebarListeners() {
        // Export marks
        document.getElementById('export-marks').addEventListener('click', () => {
            this.exportMarks();
        });

        // Clear all marks
        document.getElementById('clear-all-marks').addEventListener('click', () => {
            this.clearAllMarks();
        });
    }

    handleTradingViewClick(param, type) {
        console.log(`handleTradingViewClick called with type: ${type}`);
        console.log('TradingView param:', param);

        if (!param.point || !param.time) {
            console.warn('Invalid click parameter from TradingView');
            return;
        }

        // Get the exact candlestick data for the clicked time
        const candlestickData = this.getCandlestickAtTime(param.time);

        let price;
        if (candlestickData) {
            // Use the candlestick's close price for accuracy
            price = candlestickData.close;
        } else {
            // Fallback: use coordinate-based price
            console.warn('Could not find candlestick data, using coordinate-based price');
            try {
                const priceScale = this.chart.chart.priceScale('right');
                price = priceScale.coordinateToPrice(param.point.y);
            } catch (error) {
                console.warn('Could not get price from coordinates:', error);
                price = 50000; // Default fallback
            }
        }

        console.log(`Found candlestick:`, candlestickData);
        console.log(`Using candlestick close price: ${price} for time: ${param.time}`);

        this.currentClickData = {
            time: param.time,
            price: price,
            x: param.point.x,
            y: param.point.y,
            candlestick: candlestickData
        };

        console.log('Current click data from TradingView:', this.currentClickData);

        if (type === 'entry') {
            console.log('Showing entry modal');
            this.showEntryModal();
        } else if (type === 'exit') {
            console.log('Showing exit modal');
            this.showExitModal();
        }
    }

    handleChartClick(event, type) {
        console.log(`handleChartClick called with type: ${type}`);
        console.log('Event:', event);
        console.log('Chart container:', this.chart.container);

        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        console.log(`Click coordinates: x=${x}, y=${y}`);

        // Get time from coordinates
        let time;
        try {
            time = this.chart.chart.timeScale().coordinateToTime(x);
            console.log(`Converted time: ${time}`);
        } catch (error) {
            console.warn('Could not get time from coordinates:', error);
            time = Math.floor(Date.now() / 1000);
        }

        // Get the exact candlestick data for the clicked time
        const candlestickData = this.getCandlestickAtTime(time);

        let price;
        if (candlestickData) {
            // Use the candlestick's close price for accuracy
            price = candlestickData.close;
        } else {
            // Fallback: use coordinate-based price
            console.warn('Could not find candlestick data, using coordinate-based price');
            try {
                const priceScale = this.chart.chart.priceScale('right');
                price = priceScale.coordinateToPrice(y);
            } catch (error) {
                console.warn('Could not get price from coordinates:', error);
                price = 50000; // Default fallback
            }
        }

        console.log(`Using candlestick data:`, candlestickData);

        this.currentClickData = {
            time: candlestickData ? candlestickData.time : time, // Use candlestick time if available, otherwise use coordinate time
            price: price,
            x: x,
            y: y,
            candlestick: candlestickData
        };

        console.log('Current click data:', this.currentClickData);

        if (type === 'entry') {
            console.log('Showing entry modal');
            this.showEntryModal();
        } else if (type === 'exit') {
            console.log('Showing exit modal');
            this.showExitModal();
        }
    }

    async showEntryModal() {
        console.log('showEntryModal called');
        const modal = document.getElementById('entry-modal');

        if (!modal) {
            console.error('Entry modal not found!');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        // Populate market data
        this.setElementText('entry-symbol', symbol);
        this.setElementText('entry-timeframe', timeframe);
        this.setElementText('entry-time', formattedTime);
        this.setElementTextWithHover('entry-price', formattedPrice, this.createPriceTooltip(this.currentClickData.price));

        // Get and display OHLCV data only if we have real data
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        if (ohlcvData) {
            this.populateOHLCVData('entry-ohlcv', ohlcvData);
            // Show OHLCV section
            const ohlcvSection = document.getElementById('entry-ohlcv-section');
            if (ohlcvSection) ohlcvSection.style.display = 'block';
        } else {
            // Hide OHLCV section if no real data available
            const ohlcvSection = document.getElementById('entry-ohlcv-section');
            if (ohlcvSection) ohlcvSection.style.display = 'none';
            console.log('OHLCV section hidden - no real candlestick data available');
        }

        // Get and display indicator data
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);
        this.populateIndicatorData('entry-indicators', indicatorData);

        // Reset form
        const sideSelect = document.getElementById('entry-side');
        const quantityInput = document.getElementById('entry-quantity');
        const notesInput = document.getElementById('entry-notes');

        if (sideSelect) sideSelect.value = 'buy';
        if (quantityInput) quantityInput.value = '';
        if (notesInput) notesInput.value = '';

        modal.style.display = 'block';
        console.log('Entry modal populated and displayed');
    }

    async showExitModal() {
        console.log('showExitModal called');
        const modal = document.getElementById('exit-modal');

        if (!modal) {
            console.error('Exit modal not found!');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        // Populate market data
        this.setElementText('exit-symbol', symbol);
        this.setElementText('exit-timeframe', timeframe);
        this.setElementText('exit-time', formattedTime);
        this.setElementTextWithHover('exit-price', formattedPrice, this.createPriceTooltip(this.currentClickData.price));

        // Get and display OHLCV data only if we have real data
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        if (ohlcvData) {
            this.populateOHLCVData('exit-ohlcv', ohlcvData);
            // Show OHLCV section
            const ohlcvSection = document.getElementById('exit-ohlcv-section');
            if (ohlcvSection) ohlcvSection.style.display = 'block';
        } else {
            // Hide OHLCV section if no real data available
            const ohlcvSection = document.getElementById('exit-ohlcv-section');
            if (ohlcvSection) ohlcvSection.style.display = 'none';
            console.log('OHLCV section hidden - no real candlestick data available');
        }

        // Get and display indicator data
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);
        this.populateIndicatorData('exit-indicators', indicatorData);

        // Populate open entries
        const entrySelect = document.getElementById('exit-entry-select');
        this.populateOpenEntries(entrySelect);

        // Set up P&L calculation when entry is selected
        entrySelect.addEventListener('change', () => {
            this.updatePnLCalculation();
        });

        // Reset form
        document.getElementById('exit-quantity').value = '';
        document.getElementById('exit-notes').value = '';

        modal.style.display = 'block';
        console.log('Exit modal populated and displayed');
    }

    populateOpenEntries(selectElement) {
        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select an open entry...</option>';

        // Add open entries
        this.marks.forEach((mark, id) => {
            if (mark.status === 'open') {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)} (${mark.quantity})`;
                selectElement.appendChild(option);
            }
        });
    }

    async confirmEntry() {
        const side = document.getElementById('entry-side').value;
        const quantity = parseFloat(document.getElementById('entry-quantity').value);
        const notes = document.getElementById('entry-notes').value;

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Get comprehensive market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);

        const entryData = {
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            side: side,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: {
                timestamp: new Date(this.currentClickData.time * 1000).toISOString(),
                entry_side: side.charAt(0).toUpperCase() + side.slice(1),
                price: this.currentClickData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            }
        };

        console.log('Comprehensive entry data:', comprehensiveData);
        console.log('Entry data being sent to server:', entryData);
        console.log('Timestamp details:', {
            originalTime: this.currentClickData.time,
            timestampType: typeof this.currentClickData.time,
            dateFromTimestamp: new Date(this.currentClickData.time * 1000),
            candlestickTime: this.currentClickData.candlestick?.time
        });

        try {
            const response = await fetch('/api/v1/marks/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', errorText);
                throw new Error(`Server error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Add mark to chart
                this.addMarkToChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('entry-modal');
                console.log('Entry mark added successfully with comprehensive data');
            } else {
                alert('Error adding entry mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding entry mark:', error);
            alert('Error adding entry mark: ' + error.message);

            // For now, simulate successful entry for testing
            console.log('Simulating entry mark for testing...');
            const simulatedData = {
                id: Date.now(),
                entry_timestamp: entryData.timestamp,
                entry_price: entryData.price,
                side: entryData.side,
                quantity: entryData.quantity,
                status: 'open',
                comprehensiveData: comprehensiveData
            };

            this.addMarkToChart(simulatedData);
            this.updateSidebar();
            this.closeModal('entry-modal');
        }
    }

    async confirmExit() {
        const entryId = document.getElementById('exit-entry-select').value;
        const quantity = parseFloat(document.getElementById('exit-quantity').value);
        const notes = document.getElementById('exit-notes').value;

        if (!entryId) {
            alert('Please select an entry to exit');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid exit quantity');
            return;
        }

        // Get the selected entry for comprehensive data
        const selectedEntry = this.marks.get(entryId);
        if (!selectedEntry) {
            alert('Selected entry not found');
            return;
        }

        // Get comprehensive market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);

        const exitData = {
            entry_id: entryId,
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Calculate P&L
        let priceDiff = this.currentClickData.price - selectedEntry.entry_price;
        if (selectedEntry.side === 'sell') {
            priceDiff = -priceDiff;
        }
        const profitPct = (priceDiff / selectedEntry.entry_price) * 100;

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: selectedEntry.comprehensiveData?.entry || {
                timestamp: new Date(selectedEntry.entry_timestamp * 1000).toISOString(),
                entry_side: selectedEntry.side.charAt(0).toUpperCase() + selectedEntry.side.slice(1),
                price: selectedEntry.entry_price,
                ohlcv: selectedEntry.entry_ohlcv_data ? JSON.parse(selectedEntry.entry_ohlcv_data) : null,
                indicators: selectedEntry.entry_indicator_data ? JSON.parse(selectedEntry.entry_indicator_data) : null
            },
            exit: {
                timestamp: new Date(this.currentClickData.time * 1000).toISOString(),
                price: this.currentClickData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            },
            profit_pct: profitPct
        };

        console.log('Comprehensive exit data:', comprehensiveData);

        try {
            const response = await fetch('/api/v1/marks/exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(exitData)
            });

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Update mark on chart
                this.updateMarkOnChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('exit-modal');
                console.log('Exit mark added successfully with comprehensive data');
            } else {
                alert('Error adding exit mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding exit mark:', error);
            alert('Error adding exit mark');
        }
    }

    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        this.currentClickData = null;
    }

    getCurrentSymbol() {
        // Get current symbol from the chart or global state
        return window.currentSymbol || 'BTCUSDT';
    }

    getCurrentTimeframe() {
        // Get current timeframe from the chart or global state
        return window.currentTimeframe || '15m';
    }

    getCandlestickAtTime(targetTime) {
        // Find the exact candlestick for the given timestamp
        // Try multiple ways to access the chart data
        let chartData = null;

        // Debug current data state
        console.log('Chart currentData type:', typeof this.chart.currentData);
        console.log('Chart currentData length:', this.chart.currentData?.length);
        console.log('Chart currentData sample:', this.chart.currentData?.[0]);

        if (this.chart.currentData && this.chart.currentData.length > 0) {
            chartData = this.chart.currentData;
            console.log('Using chart.currentData with', chartData.length, 'candles');
        } else if (this.chart.dataFeed && this.chart.dataFeed.data) {
            chartData = this.chart.dataFeed.data;
            console.log('Using chart.dataFeed.data with', chartData.length, 'candles');
        } else if (this.chart.dataFeed && typeof this.chart.dataFeed.getData === 'function') {
            chartData = this.chart.dataFeed.getData();
            console.log('Using chart.dataFeed.getData() with', chartData?.length, 'candles');
        } else {
            // Try to get data from TradingView series directly
            chartData = this.tryGetDataFromSeries();

            // If still no data, try chart API
            if (!chartData || chartData.length === 0) {
                this.tryGetDataFromChartAPI(); // This is for exploration, doesn't return data yet
            }
        }

        if (!chartData || chartData.length === 0) {
            console.warn('No chart data available');
            console.log('Chart object:', this.chart);
            console.log('Available properties:', Object.keys(this.chart));
            console.log('currentData details:', {
                exists: !!this.chart.currentData,
                type: typeof this.chart.currentData,
                length: this.chart.currentData?.length,
                isArray: Array.isArray(this.chart.currentData)
            });
            return null;
        }

        console.log(`Found ${chartData.length} candles in chart data`);

        // Find the candlestick that matches the target time
        // TradingView time is usually in seconds, so we need to find the exact match
        let bestMatch = null;
        let minDifference = Infinity;

        for (const candle of chartData) {
            const timeDiff = Math.abs(candle.time - targetTime);
            if (timeDiff < minDifference) {
                minDifference = timeDiff;
                bestMatch = candle;
            }

            // If we find an exact match, use it
            if (timeDiff === 0) {
                break;
            }
        }

        if (bestMatch && minDifference < 3600) { // Within 1 hour tolerance
            console.log(`Found candlestick match with ${minDifference}s difference:`, bestMatch);
            return {
                time: bestMatch.time,
                open: bestMatch.open,
                high: bestMatch.high,
                low: bestMatch.low,
                close: bestMatch.close,
                volume: bestMatch.volume || 0
            };
        }

        // If no good match found, use the latest candle
        const latestCandle = chartData[chartData.length - 1];
        if (latestCandle) {
            console.log('Using latest candle as fallback:', latestCandle);
            return {
                time: latestCandle.time,
                open: latestCandle.open,
                high: latestCandle.high,
                low: latestCandle.low,
                close: latestCandle.close,
                volume: latestCandle.volume || 0
            };
        }

        return null;
    }

    // Try to get data directly from the TradingView series
    tryGetDataFromSeries() {
        console.log('Attempting to get data from TradingView series...');
        console.log('Candlestick series object:', this.chart.candlestickSeries);

        if (!this.chart.candlestickSeries) {
            console.log('No candlestick series available');
            return null;
        }

        try {
            // First, let's explore the series object structure
            console.log('Series object keys:', Object.keys(this.chart.candlestickSeries));
            console.log('Series object properties:', Object.getOwnPropertyNames(this.chart.candlestickSeries));

            // Try different methods to get series data
            const methods = ['data', 'getData', 'seriesData', 'history', 'dataByIndex', 'priceToCoordinate', 'coordinateToPrice'];

            for (const method of methods) {
                if (typeof this.chart.candlestickSeries[method] === 'function') {
                    console.log(`Found method: ${method}`);
                    try {
                        const data = this.chart.candlestickSeries[method]();
                        console.log(`${method}() returned:`, data);
                        if (data && Array.isArray(data) && data.length > 0) {
                            console.log(`Got data using ${method}():`, data.length, 'candles');
                            return data;
                        }
                    } catch (error) {
                        console.log(`Method ${method}() failed:`, error.message);
                    }
                }
            }

            // Try accessing data properties directly - focusing on internal TradingView properties
            const properties = [
                'data', '_data', 'seriesData', '_seriesData',
                'model', '_model', 'dataProvider', '_dataProvider',
                '_internal', '_dataSource', '_series', '_seriesModel',
                'source', '_source', 'bars', '_bars', 'points', '_points'
            ];

            for (const prop of properties) {
                if (this.chart.candlestickSeries[prop]) {
                    console.log(`Found property ${prop}:`, typeof this.chart.candlestickSeries[prop]);
                    const propValue = this.chart.candlestickSeries[prop];

                    if (Array.isArray(propValue) && propValue.length > 0) {
                        console.log(`Got data from property ${prop}:`, propValue.length, 'items');
                        console.log(`Sample data from ${prop}:`, propValue.slice(0, 2));
                        return propValue;
                    }

                    // If it's an object, explore its properties deeply
                    if (typeof propValue === 'object' && propValue !== null) {
                        console.log(`Exploring ${prop} object keys:`, Object.keys(propValue));

                        // Look for data arrays within the object
                        const subProps = [
                            'data', '_data', 'items', '_items', 'values', '_values',
                            'bars', '_bars', 'points', '_points', 'series', '_series',
                            'source', '_source', 'model', '_model'
                        ];

                        for (const subProp of subProps) {
                            if (propValue[subProp]) {
                                console.log(`Found ${prop}.${subProp}:`, typeof propValue[subProp]);

                                if (Array.isArray(propValue[subProp]) && propValue[subProp].length > 0) {
                                    console.log(`Got data from ${prop}.${subProp}:`, propValue[subProp].length, 'items');
                                    console.log(`Sample data:`, propValue[subProp].slice(0, 2));
                                    return propValue[subProp];
                                }

                                // Go deeper if it's another object
                                if (typeof propValue[subProp] === 'object' && propValue[subProp] !== null) {
                                    const deepProps = ['data', '_data', 'items', '_items', 'values', '_values'];
                                    for (const deepProp of deepProps) {
                                        if (propValue[subProp][deepProp] && Array.isArray(propValue[subProp][deepProp]) && propValue[subProp][deepProp].length > 0) {
                                            console.log(`Got data from ${prop}.${subProp}.${deepProp}:`, propValue[subProp][deepProp].length, 'items');
                                            console.log(`Sample data:`, propValue[subProp][deepProp].slice(0, 2));
                                            return propValue[subProp][deepProp];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (error) {
            console.log('Error accessing series data:', error);
        }

        return null;
    }

    async getOHLCVData(timestamp) {
        // If we have candlestick data from the click, use it directly
        if (this.currentClickData && this.currentClickData.candlestick) {
            return this.currentClickData.candlestick;
        }

        // Otherwise, find the candlestick using the timestamp
        const candlestickData = this.getCandlestickAtTime(timestamp);

        if (candlestickData) {
            return candlestickData;
        }

        // Don't create synthetic data - return null if we can't get real candlestick data
        console.warn('No candlestick data available - will not display OHLCV section');
        return null;
    }

    // Try to access data through TradingView chart API
    tryGetDataFromChartAPI() {
        console.log('Attempting to get data from TradingView chart API...');

        if (!this.chart.chart) {
            console.log('No chart API available');
            return null;
        }

        try {
            // Try to get visible range and data
            const timeScale = this.chart.chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            console.log('Visible range:', visibleRange);

            // Try to get series data through different methods
            if (this.chart.candlestickSeries) {
                console.log('Trying to access series data through chart API...');

                // Try to get data at specific coordinates
                const chartContainer = this.chart.container;
                if (chartContainer) {
                    const rect = chartContainer.getBoundingClientRect();
                    const centerX = rect.width / 2;

                    try {
                        const time = timeScale.coordinateToTime(centerX);
                        console.log('Center time:', time);

                        // This might give us access to the data at that time
                        const priceScale = this.chart.chart.priceScale('right');
                        console.log('Price scale:', priceScale);

                    } catch (error) {
                        console.log('Error getting time/price from coordinates:', error);
                    }
                }
            }

        } catch (error) {
            console.log('Error accessing chart API:', error);
        }

        return null;
    }

    async getIndicatorData(timestamp) {
        // Get indicator data if available
        const indicators = {};

        try {
            // Get EMA data if available
            if (window.indicatorsManager && window.indicatorsManager.indicatorSeries) {
                const series = window.indicatorsManager.indicatorSeries;

                // EMA indicators
                const emaData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('ema') || key.includes('EMA')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                emaData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(emaData).length > 0) {
                    indicators.ema = emaData;
                }

                // RSI indicators
                const rsiData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('rsi') || key.includes('RSI')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                rsiData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(rsiData).length > 0) {
                    indicators.rsi = rsiData;
                }

                // MACD indicators
                const macdData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('macd') || key.includes('MACD')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                macdData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(macdData).length > 0) {
                    indicators.macd = macdData;
                }

                // Bollinger Bands
                const bbData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('bollinger') || key.includes('bb') || key.includes('BB')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                bbData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(bbData).length > 0) {
                    indicators.bollinger_bands = bbData;
                }
            }

            // If no indicators are available, provide sample data for demonstration
            if (Object.keys(indicators).length === 0) {
                indicators.ema = {
                    ema_50: this.currentClickData.price * 0.998,
                    ema_100: this.currentClickData.price * 0.995,
                    ema_200: this.currentClickData.price * 0.990
                };
                indicators.rsi = {
                    rsi_6: Math.random() * 100,
                    rsi_12: Math.random() * 100,
                    rsi_24: Math.random() * 100
                };
                indicators.macd = {
                    macd_line: (Math.random() - 0.5) * 2,
                    signal_line: (Math.random() - 0.5) * 2,
                    histogram: (Math.random() - 0.5) * 0.5
                };
                indicators.bollinger_bands = {
                    basis: this.currentClickData.price,
                    upper_band: this.currentClickData.price * 1.005,
                    lower_band: this.currentClickData.price * 0.995,
                    standard_deviation: this.currentClickData.price * 0.003
                };
            }

        } catch (error) {
            console.warn('Error getting indicator data:', error);
        }

        return indicators;
    }

    setElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    setElementTextWithHover(elementId, text, tooltipContent) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <span class="hover-details">
                    ${text}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            `;
        }
    }

    createPriceTooltip(price) {
        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Exact Price:</span>
                <span class="tooltip-value">$${price.toFixed(8)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Rounded:</span>
                <span class="tooltip-value">$${price.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Timestamp:</span>
                <span class="tooltip-value">${this.currentClickData.time}</span>
            </div>
        `;
    }

    createOHLCVTooltip(ohlcvData) {
        if (!ohlcvData) return 'No OHLCV data available';

        const range = ohlcvData.high - ohlcvData.low;
        const bodySize = Math.abs(ohlcvData.close - ohlcvData.open);
        const upperWick = ohlcvData.high - Math.max(ohlcvData.open, ohlcvData.close);
        const lowerWick = Math.min(ohlcvData.open, ohlcvData.close) - ohlcvData.low;

        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Range:</span>
                <span class="tooltip-value">$${range.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Body Size:</span>
                <span class="tooltip-value">$${bodySize.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Upper Wick:</span>
                <span class="tooltip-value">$${upperWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Lower Wick:</span>
                <span class="tooltip-value">$${lowerWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Volume:</span>
                <span class="tooltip-value">${ohlcvData.volume.toLocaleString()}</span>
            </div>
        `;
    }

    populateOHLCVData(containerId, ohlcvData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!ohlcvData) {
            container.innerHTML = '<div class="data-row"><span class="data-label">No OHLCV data available</span></div>';
            return;
        }

        const tooltipContent = this.createOHLCVTooltip(ohlcvData);

        container.innerHTML = `
            <div class="data-row">
                <span class="data-label">Open:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.open.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.open.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">High:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.high.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.high.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Low:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.low.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.low.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Close:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.close.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.close.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Volume:</span>
                <span class="data-value hover-details">
                    ${ohlcvData.volume.toLocaleString()}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            </div>
        `;
    }

    populateIndicatorData(containerId, indicatorData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!indicatorData || Object.keys(indicatorData).length === 0) {
            container.innerHTML = '<div class="indicator-group"><span class="data-label">No indicator data available</span></div>';
            return;
        }

        let html = '';

        // EMA indicators
        if (indicatorData.ema) {
            html += '<div class="indicator-group">';
            html += '<h5>Exponential Moving Averages</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.ema).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // RSI indicators
        if (indicatorData.rsi) {
            html += '<div class="indicator-group">';
            html += '<h5>Relative Strength Index</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.rsi).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 70 ? 'pnl-negative' : value < 30 ? 'pnl-positive' : '';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(1)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // MACD indicators
        if (indicatorData.macd) {
            html += '<div class="indicator-group">';
            html += '<h5>MACD</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.macd).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 0 ? 'pnl-positive' : value < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(3)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // Bollinger Bands
        if (indicatorData.bollinger_bands) {
            html += '<div class="indicator-group">';
            html += '<h5>Bollinger Bands</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.bollinger_bands).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        container.innerHTML = html;
    }

    updatePnLCalculation() {
        const entrySelect = document.getElementById('exit-entry-select');
        const quantityInput = document.getElementById('exit-quantity');
        const pnlSection = document.getElementById('exit-pnl-section');
        const pnlContainer = document.getElementById('exit-pnl');

        if (!entrySelect.value || !pnlSection || !pnlContainer) {
            if (pnlSection) pnlSection.style.display = 'none';
            return;
        }

        const selectedEntry = this.marks.get(entrySelect.value);
        if (!selectedEntry) {
            pnlSection.style.display = 'none';
            return;
        }

        const entryPrice = selectedEntry.entry_price;
        const exitPrice = this.currentClickData.price;
        const quantity = parseFloat(quantityInput.value) || selectedEntry.quantity;

        // Calculate P&L
        let priceDiff = exitPrice - entryPrice;
        if (selectedEntry.side === 'sell') {
            priceDiff = -priceDiff; // Reverse for short positions
        }

        const absolutePnL = priceDiff * quantity;
        const percentagePnL = (priceDiff / entryPrice) * 100;

        // Determine color class
        const colorClass = absolutePnL > 0 ? 'pnl-positive' : absolutePnL < 0 ? 'pnl-negative' : 'pnl-neutral';

        // Populate P&L data
        pnlContainer.innerHTML = `
            <div class="data-row">
                <span class="data-label">Entry Price:</span>
                <span class="data-value">$${entryPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Exit Price:</span>
                <span class="data-value">$${exitPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Price Difference:</span>
                <span class="data-value ${colorClass}">$${priceDiff.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Quantity:</span>
                <span class="data-value">${quantity}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Absolute P&L:</span>
                <span class="data-value ${colorClass}">$${absolutePnL.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Percentage P&L:</span>
                <span class="data-value ${colorClass}">${percentagePnL.toFixed(2)}%</span>
            </div>
        `;

        pnlSection.style.display = 'block';

        // Update quantity input if empty
        if (!quantityInput.value) {
            quantityInput.value = selectedEntry.quantity;
        }

        // Add event listener to quantity input to update P&L in real-time
        quantityInput.removeEventListener('input', this.updatePnLCalculation.bind(this));
        quantityInput.addEventListener('input', this.updatePnLCalculation.bind(this));
    }

    addMarkToChart(markData) {
        // Store mark data
        this.marks.set(markData.id, markData);

        // Add visual marker to chart
        const color = markData.side === 'buy' ? '#4caf50' : '#f44336';
        
        const marker = {
            time: markData.entry_timestamp,
            position: 'belowBar',
            color: color,
            shape: 'circle',
            text: `${markData.side.toUpperCase()} @ $${markData.entry_price.toFixed(2)}`,
            size: 2
        };

        // Add marker to candlestick series
        if (this.chart.candlestickSeries) {
            const existingMarkers = this.chart.candlestickSeries.markers() || [];
            this.chart.candlestickSeries.setMarkers([...existingMarkers, marker]);
        }
    }

    updateMarkOnChart(markData) {
        // Update stored mark data
        this.marks.set(markData.id, markData);

        // Update visual representation if needed
        this.refreshChartMarkers();
    }

    refreshChartMarkers() {
        if (!this.chart.candlestickSeries) return;

        const markers = [];
        this.marks.forEach(mark => {
            const color = mark.side === 'buy' ? '#4caf50' : '#f44336';
            
            // Entry marker
            markers.push({
                time: mark.entry_timestamp,
                position: 'belowBar',
                color: color,
                shape: 'circle',
                text: `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)}`,
                size: 2
            });

            // Exit marker if exists
            if (mark.exit_timestamp) {
                markers.push({
                    time: mark.exit_timestamp,
                    position: 'aboveBar',
                    color: color,
                    shape: 'square',
                    text: `EXIT @ $${mark.exit_price.toFixed(2)}`,
                    size: 2
                });
            }
        });

        this.chart.candlestickSeries.setMarkers(markers);
    }

    async loadExistingMarks() {
        try {
            const response = await fetch('/api/v1/marks');
            const result = await response.json();
            
            if (result.success && result.data) {
                result.data.forEach(mark => {
                    this.marks.set(mark.id, mark);
                });
                
                this.refreshChartMarkers();
                this.updateSidebar();
            }
        } catch (error) {
            console.error('Error loading existing marks:', error);
        }
    }

    updateSidebar() {
        this.updateMarksList();
        this.updateStatistics();
    }

    updateMarksList() {
        const container = document.getElementById('active-marks-list');
        
        if (this.marks.size === 0) {
            container.innerHTML = '<div class="no-marks">No active marks</div>';
            return;
        }

        let html = '';
        this.marks.forEach(mark => {
            const pnl = mark.exit_price ? 
                ((mark.exit_price - mark.entry_price) * (mark.side === 'buy' ? 1 : -1) * mark.quantity).toFixed(2) : 
                'Open';
            
            html += `
                <div class="mark-item">
                    <div class="mark-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                            <span class="mark-side ${mark.side}">${mark.side}</span>
                            <span>$${mark.entry_price.toFixed(2)}</span>
                            <span style="font-size: 11px; color: #888;">${mark.quantity}</span>
                        </div>
                        <div style="font-size: 11px; color: ${pnl === 'Open' ? '#888' : (parseFloat(pnl) >= 0 ? '#4caf50' : '#f44336')};">
                            P&L: ${pnl === 'Open' ? pnl : '$' + pnl}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    updateStatistics() {
        const totalEntries = this.marks.size;
        const openPositions = Array.from(this.marks.values()).filter(m => m.status === 'open').length;
        const closedTrades = totalEntries - openPositions;
        
        const closedMarks = Array.from(this.marks.values()).filter(m => m.status === 'closed');
        const winningTrades = closedMarks.filter(m => {
            const pnl = (m.exit_price - m.entry_price) * (m.side === 'buy' ? 1 : -1);
            return pnl > 0;
        }).length;
        
        const winRate = closedTrades > 0 ? ((winningTrades / closedTrades) * 100).toFixed(1) : 0;

        document.getElementById('total-entries').textContent = totalEntries;
        document.getElementById('open-positions').textContent = openPositions;
        document.getElementById('closed-trades').textContent = closedTrades;
        document.getElementById('win-rate').textContent = winRate + '%';
    }

    enableMarkingMode() {
        console.log('enableMarkingMode called');
        this.isMarkingMode = true;
        console.log('Marking mode enabled - isMarkingMode:', this.isMarkingMode);
        console.log('Chart container available:', !!this.chart?.container);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Marking mode active - Click to add entries, right-click for exits';
            statusElement.className = 'status warning';
        }
    }

    disableMarkingMode() {
        console.log('disableMarkingMode called');
        this.isMarkingMode = false;
        console.log('Marking mode disabled - isMarkingMode:', this.isMarkingMode);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Chart ready';
            statusElement.className = 'status info';
        }
    }

    async exportMarks() {
        try {
            const response = await fetch('/api/v1/marks/export');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `marks_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting marks:', error);
            alert('Error exporting marks');
        }
    }

    async clearAllMarks() {
        if (!confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/v1/marks', {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.marks.clear();
                this.refreshChartMarkers();
                this.updateSidebar();
                console.log('All marks cleared');
            } else {
                alert('Error clearing marks: ' + result.message);
            }
        } catch (error) {
            console.error('Error clearing marks:', error);
            alert('Error clearing marks');
        }
    }
}

// Global marking tools instance
let markingTools = null;

// Test function for debugging
window.testMarkingTools = function() {
    console.log('=== Marking Tools Debug Info ===');
    console.log('markingTools instance:', markingTools);
    console.log('window.markingTools:', window.markingTools);
    console.log('window.tradingViewChart:', window.tradingViewChart);
    console.log('window.professionalChart:', window.professionalChart);
    console.log('MarkingTools class available:', typeof MarkingTools !== 'undefined');

    const activeChart = window.professionalChart || window.tradingViewChart;
    console.log('Active chart instance:', activeChart);

    if (markingTools) {
        console.log('Marking mode active:', markingTools.isMarkingMode);
        console.log('Chart available:', !!markingTools.chart);
        console.log('Chart container:', markingTools.chart?.container);

        // Debug chart data access
        console.log('=== Chart Data Debug ===');
        console.log('chart.currentData:', markingTools.chart?.currentData?.length || 'not available');
        console.log('chart.dataFeed:', !!markingTools.chart?.dataFeed);
        console.log('chart.dataFeed.data:', markingTools.chart?.dataFeed?.data?.length || 'not available');

        if (markingTools.chart?.dataFeed?.getData) {
            const data = markingTools.chart.dataFeed.getData();
            console.log('chart.dataFeed.getData():', data?.length || 'not available');
        }
    }

    // Test modal availability
    console.log('Entry modal:', document.getElementById('entry-modal'));
    console.log('Exit modal:', document.getElementById('exit-modal'));
    console.log('Marking sidebar:', document.getElementById('marking-sidebar'));

    return {
        markingTools,
        tradingViewChart: window.tradingViewChart,
        professionalChart: window.professionalChart,
        activeChart,
        modalsAvailable: {
            entry: !!document.getElementById('entry-modal'),
            exit: !!document.getElementById('exit-modal')
        }
    };
};

// Test function to simulate entry marking
window.testEntryModal = function() {
    console.log('Testing entry modal...');
    if (window.markingTools) {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };
        window.markingTools.showEntryModal();
    } else {
        console.error('Marking tools not available');
    }
};

// Test function to simulate a complete entry/exit cycle
window.testMarkingCycle = function() {
    console.log('Testing complete marking cycle...');
    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Simulate entry
    const entryData = {
        id: Date.now(),
        entry_timestamp: Math.floor(Date.now() / 1000),
        entry_price: 45000,
        side: 'buy',
        quantity: 0.1,
        status: 'open',
        comprehensiveData: {
            symbol: 'BTCUSDT',
            timeframe: '15m',
            entry: {
                timestamp: new Date().toISOString(),
                entry_side: 'Buy',
                price: 45000,
                ohlcv: {
                    open: 44950,
                    high: 45100,
                    low: 44900,
                    close: 45000,
                    volume: 1500
                },
                indicators: {
                    ema: { ema_50: 44800, ema_100: 44600, ema_200: 44400 },
                    rsi: { rsi_6: 65, rsi_12: 62, rsi_24: 58 }
                }
            }
        }
    };

    window.markingTools.addMarkToChart(entryData);
    window.markingTools.updateSidebar();

    console.log('✅ Test entry added successfully');
    console.log('You can now test the exit modal by right-clicking the chart');
};

// Debug chart data access
window.debugChartData = function() {
    console.log('=== Chart Data Access Debug ===');

    const charts = [
        { name: 'window.tradingViewChart', instance: window.tradingViewChart },
        { name: 'window.professionalChart', instance: window.professionalChart }
    ];

    charts.forEach(({ name, instance }) => {
        if (instance) {
            console.log(`\n${name}:`);
            console.log('  - Instance exists:', !!instance);
            console.log('  - currentData:', instance.currentData?.length || 'not available');
            console.log('  - dataFeed:', !!instance.dataFeed);

            if (instance.dataFeed) {
                console.log('  - dataFeed.data:', instance.dataFeed.data?.length || 'not available');
                console.log('  - dataFeed.getData:', typeof instance.dataFeed.getData);

                if (typeof instance.dataFeed.getData === 'function') {
                    try {
                        const data = instance.dataFeed.getData();
                        console.log('  - dataFeed.getData() result:', data?.length || 'not available');
                        if (data && data.length > 0) {
                            console.log('  - Sample candle:', data[data.length - 1]);
                        }
                    } catch (error) {
                        console.log('  - dataFeed.getData() error:', error.message);
                    }
                }
            }

            // Check for other possible data properties
            const possibleDataProps = ['data', 'candleData', 'ohlcData', 'chartData', 'seriesData'];
            possibleDataProps.forEach(prop => {
                if (instance[prop]) {
                    console.log(`  - ${prop}:`, instance[prop]?.length || typeof instance[prop]);
                }
            });

            // Check chart series
            if (instance.candlestickSeries) {
                console.log('  - candlestickSeries exists:', !!instance.candlestickSeries);
                // Try to get data from series (this might not work but worth trying)
                try {
                    const seriesData = instance.candlestickSeries.data();
                    console.log('  - candlestickSeries.data():', seriesData?.length || 'not available');
                } catch (error) {
                    console.log('  - candlestickSeries.data() not available');
                }
            }
        }
    });

    return { tradingViewChart: window.tradingViewChart, professionalChart: window.professionalChart };
};

// Simple test function that works without chart data
window.testSimpleEntry = function() {
    console.log('Testing simple entry without chart data...');

    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create simple click data with current timestamp
    const currentTime = Math.floor(Date.now() / 1000);
    const currentPrice = 45000 + (Math.random() - 0.5) * 1000; // Random price around 45k

    window.markingTools.currentClickData = {
        time: currentTime,
        price: currentPrice,
        x: 100,
        y: 100,
        candlestick: null // No candlestick data
    };

    console.log('Created click data:', window.markingTools.currentClickData);

    // Show entry modal
    window.markingTools.showEntryModal();

    console.log('✅ Entry modal should be displayed');
    console.log('You can now fill in the form and test the entry creation');
};

// Comprehensive chart exploration function
window.exploreChartStructure = function() {
    console.log('=== Comprehensive Chart Structure Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('No chart instance found');
        return;
    }

    console.log('Chart instance:', chart);
    console.log('Chart constructor:', chart.constructor.name);

    // Explore main chart object
    console.log('\n=== Main Chart Properties ===');
    const mainProps = Object.keys(chart);
    mainProps.forEach(prop => {
        const value = chart[prop];
        const type = typeof value;
        const isArray = Array.isArray(value);
        const length = isArray ? value.length : (value && typeof value.length === 'number' ? value.length : 'N/A');

        console.log(`${prop}: ${type}${isArray ? ' (array)' : ''} - length: ${length}`);

        if (prop === 'currentData' && isArray) {
            console.log(`  Sample currentData:`, value.slice(0, 2));
        }
    });

    // Explore candlestick series
    if (chart.candlestickSeries) {
        console.log('\n=== Candlestick Series Properties ===');
        console.log('Series object:', chart.candlestickSeries);
        console.log('Series constructor:', chart.candlestickSeries.constructor.name);

        // Get all properties including non-enumerable ones
        const seriesProps = [];
        let obj = chart.candlestickSeries;
        while (obj && obj !== Object.prototype) {
            seriesProps.push(...Object.getOwnPropertyNames(obj));
            obj = Object.getPrototypeOf(obj);
        }

        const uniqueProps = [...new Set(seriesProps)];
        console.log('All series properties:', uniqueProps);

        // Check specific properties that might contain data
        const dataProps = ['data', '_data', 'seriesData', '_seriesData', 'model', '_model', 'dataProvider', '_dataProvider'];
        dataProps.forEach(prop => {
            if (chart.candlestickSeries[prop]) {
                console.log(`${prop}:`, chart.candlestickSeries[prop]);
            }
        });
    }

    // Explore dataFeed
    if (chart.dataFeed) {
        console.log('\n=== DataFeed Properties ===');
        console.log('DataFeed object:', chart.dataFeed);
        console.log('DataFeed data length:', chart.dataFeed.data?.length);
        console.log('DataFeed sample data:', chart.dataFeed.data?.slice(-2));
    }

    // Try to get data using our method
    if (window.markingTools) {
        console.log('\n=== Testing Data Access Methods ===');
        const testData = window.markingTools.tryGetDataFromSeries();
        console.log('tryGetDataFromSeries result:', testData);
    }

    return chart;
};

// Targeted exploration of TradingView internal data structures
window.exploreTradingViewInternals = function() {
    console.log('=== TradingView Internal Data Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart || !chart.candlestickSeries) {
        console.log('No chart or candlestick series found');
        return;
    }

    const series = chart.candlestickSeries;
    console.log('Candlestick series:', series);

    // Focus on the most promising internal properties
    const internalProps = [
        '_internal', '_dataSource', '_series', '_seriesModel', '_model',
        'source', '_source', 'bars', '_bars', 'points', '_points'
    ];

    internalProps.forEach(prop => {
        if (series[prop]) {
            console.log(`\n=== ${prop} ===`);
            console.log('Type:', typeof series[prop]);
            console.log('Value:', series[prop]);

            if (typeof series[prop] === 'object' && series[prop] !== null) {
                console.log('Keys:', Object.keys(series[prop]));

                // Look for data-like properties
                const dataProps = ['data', '_data', 'items', '_items', 'values', '_values', 'bars', '_bars'];
                dataProps.forEach(dataProp => {
                    if (series[prop][dataProp]) {
                        console.log(`  ${dataProp}:`, typeof series[prop][dataProp]);
                        if (Array.isArray(series[prop][dataProp])) {
                            console.log(`    Array length: ${series[prop][dataProp].length}`);
                            if (series[prop][dataProp].length > 0) {
                                console.log(`    Sample items:`, series[prop][dataProp].slice(0, 3));
                            }
                        }
                    }
                });
            }
        }
    });

    // Also check if there are any methods that might return data
    console.log('\n=== Available Methods ===');
    const methods = [];
    let obj = series;
    while (obj && obj !== Object.prototype) {
        Object.getOwnPropertyNames(obj).forEach(name => {
            if (typeof series[name] === 'function' && !methods.includes(name)) {
                methods.push(name);
            }
        });
        obj = Object.getPrototypeOf(obj);
    }

    console.log('All methods:', methods);

    // Try some promising methods
    const dataMethods = ['data', 'getData', 'dataByIndex', 'priceToCoordinate', 'coordinateToPrice'];
    dataMethods.forEach(method => {
        if (typeof series[method] === 'function') {
            try {
                console.log(`Trying ${method}()...`);
                const result = series[method]();
                console.log(`${method}() result:`, result);
            } catch (error) {
                console.log(`${method}() error:`, error.message);
            }
        }
    });

    return series;
};

// Initialize when chart is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for chart to be initialized
    const initMarkingTools = () => {
        console.log('Checking chart readiness...');
        console.log('window.tradingViewChart:', !!window.tradingViewChart);
        console.log('window.professionalChart:', !!window.professionalChart);

        // Try both chart instances - prefer professionalChart for strategy builder
        const chartInstance = window.professionalChart || window.tradingViewChart;

        if (chartInstance && chartInstance.container) {
            markingTools = new MarkingTools(chartInstance);
            window.markingTools = markingTools;
            console.log('✅ Marking tools initialized successfully!');
            console.log('Using chart:', chartInstance === window.professionalChart ? 'professionalChart' : 'tradingViewChart');
            console.log('Chart container:', chartInstance.container);
        } else {
            console.log('⏳ Waiting for chart to be ready...');
            console.log('Available charts:', {
                tradingViewChart: !!window.tradingViewChart,
                professionalChart: !!window.professionalChart,
                containerReady: !!chartInstance?.container
            });
            setTimeout(initMarkingTools, 500);
        }
    };

    setTimeout(initMarkingTools, 1000);
});

/**
 * Marking Tools for TradingView Chart
 * Handles entry/exit marking with database integration
 */

class MarkingTools {
    constructor(chart) {
        this.chart = chart;
        this.marks = new Map(); // Store marks by ID
        this.currentClickData = null;
        this.isMarkingMode = false;
        
        this.initializeEventListeners();
        this.loadExistingMarks();
    }

    initializeEventListeners() {
        // Chart click handlers using TradingView's built-in events
        if (this.chart && this.chart.chart) {
            console.log('Setting up chart click handlers for marking tools');

            // Use TradingView's click event
            this.chart.chart.subscribeClick((param) => {
                console.log('Chart clicked via TradingView API, marking mode:', this.isMarkingMode);
                console.log('Click param:', param);

                if (this.isMarkingMode && param.point) {
                    console.log('Processing chart click for entry marking');
                    this.handleTradingViewClick(param, 'entry');
                }
            });

            // For right-click, we still need to use DOM events on the container
            if (this.chart.container) {
                this.chart.container.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    console.log('Chart container right-clicked, marking mode:', this.isMarkingMode);
                    if (this.isMarkingMode) {
                        console.log('Chart right-clicked for exit marking');
                        this.handleChartClick(e, 'exit');
                    }
                });
            }

            console.log('Chart click handlers set up successfully');
        } else {
            console.warn('Chart not ready for marking tools event listeners');
            console.log('Chart:', this.chart);
            console.log('Chart.chart:', this.chart?.chart);
            console.log('Chart.container:', this.chart?.container);
        }

        // Modal event listeners
        this.initializeModalListeners();

        // Sidebar event listeners
        this.initializeSidebarListeners();
    }

    initializeModalListeners() {
        // Entry modal
        const entryModal = document.getElementById('entry-modal');
        const entryClose = document.getElementById('entry-modal-close');
        const cancelEntry = document.getElementById('cancel-entry');
        const confirmEntry = document.getElementById('confirm-entry');

        entryClose.addEventListener('click', () => this.closeModal('entry-modal'));
        cancelEntry.addEventListener('click', () => this.closeModal('entry-modal'));
        confirmEntry.addEventListener('click', () => this.confirmEntry());

        // Exit modal
        const exitModal = document.getElementById('exit-modal');
        const exitClose = document.getElementById('exit-modal-close');
        const cancelExit = document.getElementById('cancel-exit');
        const confirmExit = document.getElementById('confirm-exit');

        exitClose.addEventListener('click', () => this.closeModal('exit-modal'));
        cancelExit.addEventListener('click', () => this.closeModal('exit-modal'));
        confirmExit.addEventListener('click', () => this.confirmExit());

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    initializeSidebarListeners() {
        // Export marks
        document.getElementById('export-marks').addEventListener('click', () => {
            this.exportMarks();
        });

        // Clear all marks
        document.getElementById('clear-all-marks').addEventListener('click', () => {
            this.clearAllMarks();
        });
    }

    handleTradingViewClick(param, type) {
        console.log(`handleTradingViewClick called with type: ${type}`);
        console.log('TradingView param:', param);

        if (!param.point || !param.time) {
            console.warn('Invalid click parameter from TradingView');
            return;
        }

        // Get price from coordinates using TradingView API
        let price;
        try {
            price = this.chart.chart.priceScale('right').coordinateToPrice(param.point.y);
            console.log(`TradingView click - time: ${param.time}, price: ${price}`);
        } catch (error) {
            console.warn('Could not get price from TradingView coordinates:', error);
            price = 50000; // Default price
        }

        this.currentClickData = {
            time: param.time,
            price: price,
            x: param.point.x,
            y: param.point.y
        };

        console.log('Current click data from TradingView:', this.currentClickData);

        if (type === 'entry') {
            console.log('Showing entry modal');
            this.showEntryModal();
        } else if (type === 'exit') {
            console.log('Showing exit modal');
            this.showExitModal();
        }
    }

    handleChartClick(event, type) {
        console.log(`handleChartClick called with type: ${type}`);
        console.log('Event:', event);
        console.log('Chart container:', this.chart.container);

        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        console.log(`Click coordinates: x=${x}, y=${y}`);

        // Get time and price from coordinates
        let time, price;

        try {
            time = this.chart.chart.timeScale().coordinateToTime(x);
            price = this.chart.chart.priceScale('right').coordinateToPrice(y);
            console.log(`Converted to: time=${time}, price=${price}`);
        } catch (error) {
            console.warn('Could not get time/price from coordinates:', error);
            // Fallback: use current time and a reasonable price
            time = Math.floor(Date.now() / 1000);
            price = 50000; // Default price
            console.log(`Using fallback: time=${time}, price=${price}`);
        }

        if (!time || !price) {
            console.warn('Could not get time/price from coordinates');
            return;
        }

        this.currentClickData = {
            time: time,
            price: price,
            x: x,
            y: y
        };

        console.log('Current click data:', this.currentClickData);

        if (type === 'entry') {
            console.log('Showing entry modal');
            this.showEntryModal();
        } else if (type === 'exit') {
            console.log('Showing exit modal');
            this.showExitModal();
        }
    }

    async showEntryModal() {
        console.log('showEntryModal called');
        const modal = document.getElementById('entry-modal');

        if (!modal) {
            console.error('Entry modal not found!');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        // Populate market data
        this.setElementText('entry-symbol', symbol);
        this.setElementText('entry-timeframe', timeframe);
        this.setElementText('entry-time', formattedTime);
        this.setElementText('entry-price', formattedPrice);

        // Get and display OHLCV data
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        this.populateOHLCVData('entry-ohlcv', ohlcvData);

        // Get and display indicator data
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);
        this.populateIndicatorData('entry-indicators', indicatorData);

        // Reset form
        const sideSelect = document.getElementById('entry-side');
        const quantityInput = document.getElementById('entry-quantity');
        const notesInput = document.getElementById('entry-notes');

        if (sideSelect) sideSelect.value = 'buy';
        if (quantityInput) quantityInput.value = '';
        if (notesInput) notesInput.value = '';

        modal.style.display = 'block';
        console.log('Entry modal populated and displayed');
    }

    async showExitModal() {
        console.log('showExitModal called');
        const modal = document.getElementById('exit-modal');

        if (!modal) {
            console.error('Exit modal not found!');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const date = new Date(this.currentClickData.time * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${this.currentClickData.price.toFixed(2)}`;

        // Populate market data
        this.setElementText('exit-symbol', symbol);
        this.setElementText('exit-timeframe', timeframe);
        this.setElementText('exit-time', formattedTime);
        this.setElementText('exit-price', formattedPrice);

        // Get and display OHLCV data
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        this.populateOHLCVData('exit-ohlcv', ohlcvData);

        // Get and display indicator data
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);
        this.populateIndicatorData('exit-indicators', indicatorData);

        // Populate open entries
        const entrySelect = document.getElementById('exit-entry-select');
        this.populateOpenEntries(entrySelect);

        // Set up P&L calculation when entry is selected
        entrySelect.addEventListener('change', () => {
            this.updatePnLCalculation();
        });

        // Reset form
        document.getElementById('exit-quantity').value = '';
        document.getElementById('exit-notes').value = '';

        modal.style.display = 'block';
        console.log('Exit modal populated and displayed');
    }

    populateOpenEntries(selectElement) {
        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select an open entry...</option>';

        // Add open entries
        this.marks.forEach((mark, id) => {
            if (mark.status === 'open') {
                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)} (${mark.quantity})`;
                selectElement.appendChild(option);
            }
        });
    }

    async confirmEntry() {
        const side = document.getElementById('entry-side').value;
        const quantity = parseFloat(document.getElementById('entry-quantity').value);
        const notes = document.getElementById('entry-notes').value;

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Get comprehensive market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);

        const entryData = {
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            side: side,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: {
                timestamp: new Date(this.currentClickData.time * 1000).toISOString(),
                entry_side: side.charAt(0).toUpperCase() + side.slice(1),
                price: this.currentClickData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            }
        };

        console.log('Comprehensive entry data:', comprehensiveData);

        try {
            const response = await fetch('/api/v1/marks/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryData)
            });

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Add mark to chart
                this.addMarkToChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('entry-modal');
                console.log('Entry mark added successfully with comprehensive data');
            } else {
                alert('Error adding entry mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding entry mark:', error);
            alert('Error adding entry mark');
        }
    }

    async confirmExit() {
        const entryId = document.getElementById('exit-entry-select').value;
        const quantity = parseFloat(document.getElementById('exit-quantity').value);
        const notes = document.getElementById('exit-notes').value;

        if (!entryId) {
            alert('Please select an entry to exit');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid exit quantity');
            return;
        }

        // Get the selected entry for comprehensive data
        const selectedEntry = this.marks.get(entryId);
        if (!selectedEntry) {
            alert('Selected entry not found');
            return;
        }

        // Get comprehensive market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();
        const ohlcvData = await this.getOHLCVData(this.currentClickData.time);
        const indicatorData = await this.getIndicatorData(this.currentClickData.time);

        const exitData = {
            entry_id: entryId,
            timestamp: this.currentClickData.time,
            price: this.currentClickData.price,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Calculate P&L
        let priceDiff = this.currentClickData.price - selectedEntry.entry_price;
        if (selectedEntry.side === 'sell') {
            priceDiff = -priceDiff;
        }
        const profitPct = (priceDiff / selectedEntry.entry_price) * 100;

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: selectedEntry.comprehensiveData?.entry || {
                timestamp: new Date(selectedEntry.entry_timestamp * 1000).toISOString(),
                entry_side: selectedEntry.side.charAt(0).toUpperCase() + selectedEntry.side.slice(1),
                price: selectedEntry.entry_price,
                ohlcv: selectedEntry.entry_ohlcv_data ? JSON.parse(selectedEntry.entry_ohlcv_data) : null,
                indicators: selectedEntry.entry_indicator_data ? JSON.parse(selectedEntry.entry_indicator_data) : null
            },
            exit: {
                timestamp: new Date(this.currentClickData.time * 1000).toISOString(),
                price: this.currentClickData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            },
            profit_pct: profitPct
        };

        console.log('Comprehensive exit data:', comprehensiveData);

        try {
            const response = await fetch('/api/v1/marks/exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(exitData)
            });

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Update mark on chart
                this.updateMarkOnChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('exit-modal');
                console.log('Exit mark added successfully with comprehensive data');
            } else {
                alert('Error adding exit mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding exit mark:', error);
            alert('Error adding exit mark');
        }
    }

    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        this.currentClickData = null;
    }

    getCurrentSymbol() {
        // Get current symbol from the chart or global state
        return window.currentSymbol || 'BTCUSDT';
    }

    getCurrentTimeframe() {
        // Get current timeframe from the chart or global state
        return window.currentTimeframe || '15m';
    }

    async getOHLCVData(timestamp) {
        // Get OHLCV data from the chart at the specific timestamp
        if (!this.chart.currentData) {
            console.warn('No chart data available');
            return null;
        }

        // Find the closest candle to the timestamp
        const candle = this.chart.currentData.find(c => Math.abs(c.time - timestamp) < 60);

        if (candle) {
            return {
                open: candle.open,
                high: candle.high,
                low: candle.low,
                close: candle.close,
                volume: candle.volume || 0
            };
        }

        // If no exact match, get the latest candle
        const latestCandle = this.chart.currentData[this.chart.currentData.length - 1];
        if (latestCandle) {
            return {
                open: latestCandle.open,
                high: latestCandle.high,
                low: latestCandle.low,
                close: latestCandle.close,
                volume: latestCandle.volume || 0
            };
        }

        return null;
    }

    async getIndicatorData(timestamp) {
        // Get indicator data if available
        const indicators = {};

        try {
            // Get EMA data if available
            if (window.indicatorsManager && window.indicatorsManager.indicatorSeries) {
                const series = window.indicatorsManager.indicatorSeries;

                // EMA indicators
                const emaData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('ema') || key.includes('EMA')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                emaData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(emaData).length > 0) {
                    indicators.ema = emaData;
                }

                // RSI indicators
                const rsiData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('rsi') || key.includes('RSI')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                rsiData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(rsiData).length > 0) {
                    indicators.rsi = rsiData;
                }

                // MACD indicators
                const macdData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('macd') || key.includes('MACD')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                macdData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(macdData).length > 0) {
                    indicators.macd = macdData;
                }

                // Bollinger Bands
                const bbData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('bollinger') || key.includes('bb') || key.includes('BB')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                bbData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(bbData).length > 0) {
                    indicators.bollinger_bands = bbData;
                }
            }

            // If no indicators are available, provide sample data for demonstration
            if (Object.keys(indicators).length === 0) {
                indicators.ema = {
                    ema_50: this.currentClickData.price * 0.998,
                    ema_100: this.currentClickData.price * 0.995,
                    ema_200: this.currentClickData.price * 0.990
                };
                indicators.rsi = {
                    rsi_6: Math.random() * 100,
                    rsi_12: Math.random() * 100,
                    rsi_24: Math.random() * 100
                };
                indicators.macd = {
                    macd_line: (Math.random() - 0.5) * 2,
                    signal_line: (Math.random() - 0.5) * 2,
                    histogram: (Math.random() - 0.5) * 0.5
                };
                indicators.bollinger_bands = {
                    basis: this.currentClickData.price,
                    upper_band: this.currentClickData.price * 1.005,
                    lower_band: this.currentClickData.price * 0.995,
                    standard_deviation: this.currentClickData.price * 0.003
                };
            }

        } catch (error) {
            console.warn('Error getting indicator data:', error);
        }

        return indicators;
    }

    setElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    populateOHLCVData(containerId, ohlcvData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!ohlcvData) {
            container.innerHTML = '<div class="data-row"><span class="data-label">No OHLCV data available</span></div>';
            return;
        }

        container.innerHTML = `
            <div class="data-row">
                <span class="data-label">Open:</span>
                <span class="data-value">$${ohlcvData.open.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">High:</span>
                <span class="data-value">$${ohlcvData.high.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Low:</span>
                <span class="data-value">$${ohlcvData.low.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Close:</span>
                <span class="data-value">$${ohlcvData.close.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Volume:</span>
                <span class="data-value">${ohlcvData.volume.toLocaleString()}</span>
            </div>
        `;
    }

    populateIndicatorData(containerId, indicatorData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!indicatorData || Object.keys(indicatorData).length === 0) {
            container.innerHTML = '<div class="indicator-group"><span class="data-label">No indicator data available</span></div>';
            return;
        }

        let html = '';

        // EMA indicators
        if (indicatorData.ema) {
            html += '<div class="indicator-group">';
            html += '<h5>Exponential Moving Averages</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.ema).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // RSI indicators
        if (indicatorData.rsi) {
            html += '<div class="indicator-group">';
            html += '<h5>Relative Strength Index</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.rsi).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 70 ? 'pnl-negative' : value < 30 ? 'pnl-positive' : '';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(1)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // MACD indicators
        if (indicatorData.macd) {
            html += '<div class="indicator-group">';
            html += '<h5>MACD</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.macd).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 0 ? 'pnl-positive' : value < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(3)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // Bollinger Bands
        if (indicatorData.bollinger_bands) {
            html += '<div class="indicator-group">';
            html += '<h5>Bollinger Bands</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.bollinger_bands).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        container.innerHTML = html;
    }

    updatePnLCalculation() {
        const entrySelect = document.getElementById('exit-entry-select');
        const quantityInput = document.getElementById('exit-quantity');
        const pnlSection = document.getElementById('exit-pnl-section');
        const pnlContainer = document.getElementById('exit-pnl');

        if (!entrySelect.value || !pnlSection || !pnlContainer) {
            if (pnlSection) pnlSection.style.display = 'none';
            return;
        }

        const selectedEntry = this.marks.get(entrySelect.value);
        if (!selectedEntry) {
            pnlSection.style.display = 'none';
            return;
        }

        const entryPrice = selectedEntry.entry_price;
        const exitPrice = this.currentClickData.price;
        const quantity = parseFloat(quantityInput.value) || selectedEntry.quantity;

        // Calculate P&L
        let priceDiff = exitPrice - entryPrice;
        if (selectedEntry.side === 'sell') {
            priceDiff = -priceDiff; // Reverse for short positions
        }

        const absolutePnL = priceDiff * quantity;
        const percentagePnL = (priceDiff / entryPrice) * 100;

        // Determine color class
        const colorClass = absolutePnL > 0 ? 'pnl-positive' : absolutePnL < 0 ? 'pnl-negative' : 'pnl-neutral';

        // Populate P&L data
        pnlContainer.innerHTML = `
            <div class="data-row">
                <span class="data-label">Entry Price:</span>
                <span class="data-value">$${entryPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Exit Price:</span>
                <span class="data-value">$${exitPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Price Difference:</span>
                <span class="data-value ${colorClass}">$${priceDiff.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Quantity:</span>
                <span class="data-value">${quantity}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Absolute P&L:</span>
                <span class="data-value ${colorClass}">$${absolutePnL.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Percentage P&L:</span>
                <span class="data-value ${colorClass}">${percentagePnL.toFixed(2)}%</span>
            </div>
        `;

        pnlSection.style.display = 'block';

        // Update quantity input if empty
        if (!quantityInput.value) {
            quantityInput.value = selectedEntry.quantity;
        }

        // Add event listener to quantity input to update P&L in real-time
        quantityInput.removeEventListener('input', this.updatePnLCalculation.bind(this));
        quantityInput.addEventListener('input', this.updatePnLCalculation.bind(this));
    }

    addMarkToChart(markData) {
        // Store mark data
        this.marks.set(markData.id, markData);

        // Add visual marker to chart
        const color = markData.side === 'buy' ? '#4caf50' : '#f44336';
        
        const marker = {
            time: markData.entry_timestamp,
            position: 'belowBar',
            color: color,
            shape: 'circle',
            text: `${markData.side.toUpperCase()} @ $${markData.entry_price.toFixed(2)}`,
            size: 2
        };

        // Add marker to candlestick series
        if (this.chart.candlestickSeries) {
            const existingMarkers = this.chart.candlestickSeries.markers() || [];
            this.chart.candlestickSeries.setMarkers([...existingMarkers, marker]);
        }
    }

    updateMarkOnChart(markData) {
        // Update stored mark data
        this.marks.set(markData.id, markData);

        // Update visual representation if needed
        this.refreshChartMarkers();
    }

    refreshChartMarkers() {
        if (!this.chart.candlestickSeries) return;

        const markers = [];
        this.marks.forEach(mark => {
            const color = mark.side === 'buy' ? '#4caf50' : '#f44336';
            
            // Entry marker
            markers.push({
                time: mark.entry_timestamp,
                position: 'belowBar',
                color: color,
                shape: 'circle',
                text: `${mark.side.toUpperCase()} @ $${mark.entry_price.toFixed(2)}`,
                size: 2
            });

            // Exit marker if exists
            if (mark.exit_timestamp) {
                markers.push({
                    time: mark.exit_timestamp,
                    position: 'aboveBar',
                    color: color,
                    shape: 'square',
                    text: `EXIT @ $${mark.exit_price.toFixed(2)}`,
                    size: 2
                });
            }
        });

        this.chart.candlestickSeries.setMarkers(markers);
    }

    async loadExistingMarks() {
        try {
            const response = await fetch('/api/v1/marks');
            const result = await response.json();
            
            if (result.success && result.data) {
                result.data.forEach(mark => {
                    this.marks.set(mark.id, mark);
                });
                
                this.refreshChartMarkers();
                this.updateSidebar();
            }
        } catch (error) {
            console.error('Error loading existing marks:', error);
        }
    }

    updateSidebar() {
        this.updateMarksList();
        this.updateStatistics();
    }

    updateMarksList() {
        const container = document.getElementById('active-marks-list');
        
        if (this.marks.size === 0) {
            container.innerHTML = '<div class="no-marks">No active marks</div>';
            return;
        }

        let html = '';
        this.marks.forEach(mark => {
            const pnl = mark.exit_price ? 
                ((mark.exit_price - mark.entry_price) * (mark.side === 'buy' ? 1 : -1) * mark.quantity).toFixed(2) : 
                'Open';
            
            html += `
                <div class="mark-item">
                    <div class="mark-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                            <span class="mark-side ${mark.side}">${mark.side}</span>
                            <span>$${mark.entry_price.toFixed(2)}</span>
                            <span style="font-size: 11px; color: #888;">${mark.quantity}</span>
                        </div>
                        <div style="font-size: 11px; color: ${pnl === 'Open' ? '#888' : (parseFloat(pnl) >= 0 ? '#4caf50' : '#f44336')};">
                            P&L: ${pnl === 'Open' ? pnl : '$' + pnl}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    updateStatistics() {
        const totalEntries = this.marks.size;
        const openPositions = Array.from(this.marks.values()).filter(m => m.status === 'open').length;
        const closedTrades = totalEntries - openPositions;
        
        const closedMarks = Array.from(this.marks.values()).filter(m => m.status === 'closed');
        const winningTrades = closedMarks.filter(m => {
            const pnl = (m.exit_price - m.entry_price) * (m.side === 'buy' ? 1 : -1);
            return pnl > 0;
        }).length;
        
        const winRate = closedTrades > 0 ? ((winningTrades / closedTrades) * 100).toFixed(1) : 0;

        document.getElementById('total-entries').textContent = totalEntries;
        document.getElementById('open-positions').textContent = openPositions;
        document.getElementById('closed-trades').textContent = closedTrades;
        document.getElementById('win-rate').textContent = winRate + '%';
    }

    enableMarkingMode() {
        console.log('enableMarkingMode called');
        this.isMarkingMode = true;
        console.log('Marking mode enabled - isMarkingMode:', this.isMarkingMode);
        console.log('Chart container available:', !!this.chart?.container);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Marking mode active - Click to add entries, right-click for exits';
            statusElement.className = 'status warning';
        }
    }

    disableMarkingMode() {
        console.log('disableMarkingMode called');
        this.isMarkingMode = false;
        console.log('Marking mode disabled - isMarkingMode:', this.isMarkingMode);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Chart ready';
            statusElement.className = 'status info';
        }
    }

    async exportMarks() {
        try {
            const response = await fetch('/api/v1/marks/export');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `marks_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting marks:', error);
            alert('Error exporting marks');
        }
    }

    async clearAllMarks() {
        if (!confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/v1/marks', {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.marks.clear();
                this.refreshChartMarkers();
                this.updateSidebar();
                console.log('All marks cleared');
            } else {
                alert('Error clearing marks: ' + result.message);
            }
        } catch (error) {
            console.error('Error clearing marks:', error);
            alert('Error clearing marks');
        }
    }
}

// Global marking tools instance
let markingTools = null;

// Test function for debugging
window.testMarkingTools = function() {
    console.log('=== Marking Tools Debug Info ===');
    console.log('markingTools instance:', markingTools);
    console.log('window.markingTools:', window.markingTools);
    console.log('window.tradingViewChart:', window.tradingViewChart);
    console.log('window.professionalChart:', window.professionalChart);
    console.log('MarkingTools class available:', typeof MarkingTools !== 'undefined');

    const activeChart = window.professionalChart || window.tradingViewChart;
    console.log('Active chart instance:', activeChart);

    if (markingTools) {
        console.log('Marking mode active:', markingTools.isMarkingMode);
        console.log('Chart available:', !!markingTools.chart);
        console.log('Chart container:', markingTools.chart?.container);
    }

    // Test modal availability
    console.log('Entry modal:', document.getElementById('entry-modal'));
    console.log('Exit modal:', document.getElementById('exit-modal'));
    console.log('Marking sidebar:', document.getElementById('marking-sidebar'));

    return {
        markingTools,
        tradingViewChart: window.tradingViewChart,
        professionalChart: window.professionalChart,
        activeChart,
        modalsAvailable: {
            entry: !!document.getElementById('entry-modal'),
            exit: !!document.getElementById('exit-modal')
        }
    };
};

// Test function to simulate entry marking
window.testEntryModal = function() {
    console.log('Testing entry modal...');
    if (window.markingTools) {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };
        window.markingTools.showEntryModal();
    } else {
        console.error('Marking tools not available');
    }
};

// Initialize when chart is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for chart to be initialized
    const initMarkingTools = () => {
        console.log('Checking chart readiness...');
        console.log('window.tradingViewChart:', !!window.tradingViewChart);
        console.log('window.professionalChart:', !!window.professionalChart);

        // Try both chart instances - prefer professionalChart for strategy builder
        const chartInstance = window.professionalChart || window.tradingViewChart;

        if (chartInstance && chartInstance.container) {
            markingTools = new MarkingTools(chartInstance);
            window.markingTools = markingTools;
            console.log('✅ Marking tools initialized successfully!');
            console.log('Using chart:', chartInstance === window.professionalChart ? 'professionalChart' : 'tradingViewChart');
            console.log('Chart container:', chartInstance.container);
        } else {
            console.log('⏳ Waiting for chart to be ready...');
            console.log('Available charts:', {
                tradingViewChart: !!window.tradingViewChart,
                professionalChart: !!window.professionalChart,
                containerReady: !!chartInstance?.container
            });
            setTimeout(initMarkingTools, 500);
        }
    };

    setTimeout(initMarkingTools, 1000);
});

# Strategy Builder - Phase 1 MVP

A comprehensive trading strategy development platform with interactive chart analysis, technical indicators, and trade marking capabilities.

## 🚀 Features

### Phase 1 MVP Features

- **Interactive Trading Charts**: TradingView-style candlestick charts with zoom, pan, and responsive design
- **Multi-Exchange Support**: Fetch data from Binance and MEXC exchanges
- **Technical Indicators**: RSI, MACD, EMA, SMA, and Bollinger Bands with configurable parameters
- **Trade Marking System**: Mark entry/exit points directly on charts with profit calculation
- **Data Storage**: MySQL database for OHLCV data, indicators, and trade history
- **Strategy Analysis**: Track trade performance with detailed statistics
- **Data Export**: Export trade marks and strategy logs in JSON format

### Supported Timeframes

- 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w

### Supported Exchanges

- **Binance Futures**: Full API integration with real-time data
- **MEXC Futures**: Complete API support with historical data

## 📁 Project Structure

```
Strategy_builder/
├── backend/                 # FastAPI backend application
│   └── app/
│       ├── api/            # API endpoints
│       ├── core/           # Core configuration and database
│       ├── models/         # SQLAlchemy database models
│       ├── services/       # Business logic and external APIs
│       └── utils/          # Utility functions
├── frontend/               # Web frontend
│   ├── static/
│   │   ├── css/           # Stylesheets
│   │   └── js/            # JavaScript modules
│   └── templates/         # HTML templates
├── database/              # Database scripts and migrations
├── config/                # Configuration files
├── tests/                 # Unit and integration tests
├── docs/                  # Documentation
└── scripts/               # Utility scripts
```

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.8+
- MySQL 8.0+
- Node.js (for frontend dependencies, optional)

### 1. Clone Repository

```bash
git clone <repository-url>
cd Strategy_builder
```

### 2. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure Environment

Copy and edit the `.env` file with your credentials:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=strategy_builder

# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_secret

# MEXC API Configuration
MEXC_API_KEY=your_mexc_api_key
MEXC_API_SECRET=your_mexc_secret
```

### 4. Setup Database

```bash
python scripts/setup_database.py
```

### 5. Test Database Connection (Optional)

```bash
python scripts/test_pymysql.py
```

### 6. Start the Server

```bash
python scripts/run_server.py
```

The application will be available at: `http://localhost:8000`

## 📖 Usage Guide

### 1. Fetch Market Data

1. Select exchange (Binance/MEXC)
2. Enter trading symbol (e.g., BTCUSDT)
3. Choose timeframe and number of candles
4. Click "Fetch Data" to get data from exchange
5. Or click "Load from DB" to use stored data

### 2. Add Technical Indicators

1. Enable desired indicators (RSI, MACD, EMA, SMA)
2. Configure parameters (periods, etc.)
3. Click "Calculate Indicators"
4. Indicators will overlay on the chart

### 3. Mark Trades

1. Click "Mark Entry" and then click on chart at entry point
2. Select Buy/Sell side
3. Click "Mark Exit" and click on chart at exit point
4. Click "Complete Trade" to calculate profit and store

### 4. Analyze Performance

1. View trade summary in the trade panel
2. Click "View Strategy Log" for detailed statistics
3. Export data using "Export Marks" or "Export Trades"

## 🔧 API Documentation

### OHLCV Endpoints

- `GET /api/v1/ohlcv/fetch` - Fetch data from exchange
- `GET /api/v1/ohlcv/data` - Get stored OHLCV data
- `GET /api/v1/ohlcv/symbols` - Get available symbols

### Indicators Endpoints

- `POST /api/v1/indicators/calculate` - Calculate technical indicators
- `GET /api/v1/indicators/data` - Get stored indicators
- `POST /api/v1/indicators/snapshot` - Get indicator values at timestamp

### Trade Management Endpoints

- `POST /api/v1/trades/mark` - Create trade mark
- `POST /api/v1/trades/complete-trade` - Complete trade
- `GET /api/v1/trades/marks` - Get trade marks
- `GET /api/v1/trades/strategy-log` - Get strategy log

Full API documentation available at: `http://localhost:8000/api/docs`

## 🧪 Testing

### Run Unit Tests

```bash
pytest tests/unit/
```

### Run Integration Tests

```bash
pytest tests/integration/
```

### Test Exchange Connections

```bash
python -c "
from backend.app.services.binance_client import BinanceClient
from backend.app.services.mexc_client import MEXCClient

binance = BinanceClient()
mexc = MEXCClient()

print('Binance:', binance.test_connection())
print('MEXC:', mexc.test_connection())
"
```

## 📊 Database Schema

### Tables

- **ohlcv_data**: Candlestick data (symbol, timeframe, OHLCV, timestamp)
- **indicators_data**: Technical indicator values
- **manual_marks**: Trade entry/exit marks
- **strategy_log**: Completed trades with profit calculations

## 🔧 Technology Stack

- **Backend**: FastAPI, SQLAlchemy, MySQL with PyMySQL
- **Frontend**: HTML5, CSS3, JavaScript, TradingView Charts
- **Indicators**: pandas-ta for technical analysis
- **APIs**: Binance & MEXC REST APIs
- **Database**: PyMySQL connector for reliable MySQL connections

## 🔮 Future Enhancements (Phase 2+)

- **Strategy Rule Generator**: AI-powered pattern recognition
- **Backtesting Engine**: Test strategies on historical data
- **Advanced Indicators**: Custom indicator support
- **Real-time Data**: WebSocket connections for live data
- **Portfolio Management**: Multi-symbol strategy tracking
- **Cloud Deployment**: Docker containerization and cloud hosting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:

1. Check the documentation
2. Review API docs at `/api/docs`
3. Check database connection and credentials
4. Verify exchange API keys and permissions

## 🔧 Troubleshooting

### Common Issues

**Database Connection Failed**

- Verify MySQL is running
- Check credentials in `.env` file
- Ensure database exists
- Test PyMySQL connection: `python scripts/test_pymysql.py`
- Verify charset is set to utf8mb4

**Exchange API Errors**

- Verify API keys are correct
- Check API permissions
- Ensure network connectivity

**Chart Not Loading**

- Check browser console for errors
- Verify data was fetched successfully
- Refresh the page

**Indicators Not Calculating**

- Ensure sufficient OHLCV data is available
- Check indicator parameters are valid
- Verify data quality

/**
 * Professional TradingView Chart Implementation with Infinite History
 * Uses official TradingView Lightweight Charts library with real Binance data
 * Implements infinite scrolling for historical data loading
 */

/**
 * Data Feed Class for managing historical data with infinite loading
 */
class BinanceDataFeed {
    constructor(symbol, interval) {
        this.symbol = symbol;
        this.interval = interval;
        this.data = [];
        this.earliestTime = null;
        this.isLoading = false;
        this.hasMoreData = true;
    }

    async loadInitialData(limit = 500) {
        try {
            const response = await fetch(`/api/v1/ohlcv/fetch?symbol=${this.symbol}&timeframe=${this.interval}&exchange=binance&limit=${limit}`);
            const result = await response.json();

            if (result.success && result.data && result.data.ohlcv) {
                this.data = this.processOHLCVData(result.data.ohlcv);
                if (this.data.length > 0) {
                    this.earliestTime = this.data[0].time;
                }
                return this.data;
            } else {
                throw new Error('Failed to load initial data');
            }
        } catch (error) {
            console.error('Error loading initial data:', error);
            return this.generateSampleData(limit);
        }
    }

    async loadMoreData(numberOfBars = 500) {
        if (this.isLoading || !this.hasMoreData) {
            return this.data;
        }

        this.isLoading = true;

        try {
            // Calculate the end time for the historical data request
            const endTime = new Date(this.earliestTime * 1000);
            endTime.setMinutes(endTime.getMinutes() - this.getIntervalMinutes());

            const response = await fetch(
                `/api/v1/ohlcv/fetch?symbol=${this.symbol}&timeframe=${this.interval}&exchange=binance&limit=${numberOfBars}&end_time=${endTime.toISOString()}`
            );
            const result = await response.json();

            if (result.success && result.data && result.data.ohlcv && result.data.ohlcv.length > 0) {
                const newData = this.processOHLCVData(result.data.ohlcv);

                // Prepend new data to existing data
                this.data = [...newData, ...this.data];
                this.earliestTime = newData[0].time;

                // Check if we've reached the limit of available data
                if (newData.length < numberOfBars) {
                    this.hasMoreData = false;
                }
            } else {
                // If API fails, generate sample historical data
                const newData = this.generateHistoricalSampleData(numberOfBars);
                this.data = [...newData, ...this.data];
                this.earliestTime = newData[0].time;
            }
        } catch (error) {
            console.error('Error loading more data:', error);
            // Generate sample data as fallback
            const newData = this.generateHistoricalSampleData(numberOfBars);
            this.data = [...newData, ...this.data];
            this.earliestTime = newData[0].time;
        } finally {
            this.isLoading = false;
        }

        return this.data;
    }

    processOHLCVData(ohlcvData) {
        return ohlcvData
            .map(item => {
                try {
                    let timestamp;
                    if (typeof item.timestamp === 'number') {
                        timestamp = item.timestamp > 1000000000000 ?
                            Math.floor(item.timestamp / 1000) :
                            Math.floor(item.timestamp);
                    } else {
                        timestamp = Math.floor(new Date(item.timestamp).getTime() / 1000);
                    }

                    return {
                        time: timestamp,
                        open: parseFloat(item.open),
                        high: parseFloat(item.high),
                        low: parseFloat(item.low),
                        close: parseFloat(item.close),
                        volume: parseFloat(item.volume)
                    };
                } catch (error) {
                    console.warn('Error processing OHLCV item:', item, error);
                    return null;
                }
            })
            .filter(item => item !== null)
            .sort((a, b) => a.time - b.time);
    }

    generateSampleData(numberOfBars) {
        const data = [];
        const intervalMinutes = this.getIntervalMinutes();
        const endTime = Math.floor(Date.now() / 1000);
        const startTime = endTime - (numberOfBars * intervalMinutes * 60);

        let basePrice = this.symbol.includes('BTC') ? 45000 :
                       this.symbol.includes('ETH') ? 3000 : 100;

        for (let i = 0; i < numberOfBars; i++) {
            const time = startTime + (i * intervalMinutes * 60);
            const volatility = basePrice * 0.002;

            const open = i === 0 ? basePrice : data[i - 1].close;
            const change = (Math.random() - 0.5) * volatility * 2;
            const close = open + change;
            const high = Math.max(open, close) + Math.random() * volatility;
            const low = Math.min(open, close) - Math.random() * volatility;
            const volume = Math.random() * 1000000 + 100000;

            data.push({
                time: time,
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2)),
                volume: parseFloat(volume.toFixed(0))
            });
        }

        this.earliestTime = data[0].time;
        return data;
    }

    generateHistoricalSampleData(numberOfBars) {
        const data = [];
        const intervalMinutes = this.getIntervalMinutes();
        const startTime = this.earliestTime - (numberOfBars * intervalMinutes * 60);

        // Get the last known price to continue the trend
        const lastPrice = this.data.length > 0 ? this.data[0].open : 45000;

        for (let i = 0; i < numberOfBars; i++) {
            const time = startTime + (i * intervalMinutes * 60);
            const volatility = lastPrice * 0.002;

            const open = i === 0 ? lastPrice * (0.98 + Math.random() * 0.04) : data[i - 1].close;
            const change = (Math.random() - 0.5) * volatility * 2;
            const close = open + change;
            const high = Math.max(open, close) + Math.random() * volatility;
            const low = Math.min(open, close) - Math.random() * volatility;
            const volume = Math.random() * 1000000 + 100000;

            data.push({
                time: time,
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2)),
                volume: parseFloat(volume.toFixed(0))
            });
        }

        return data;
    }

    getIntervalMinutes() {
        const intervalMap = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
        };
        return intervalMap[this.interval] || 1;
    }

    getData() {
        return this.data;
    }

    getVolumeData() {
        return this.data.map(item => ({
            time: item.time,
            value: item.volume,
            color: item.close >= item.open ? '#26a69a80' : '#ef535080'
        }));
    }
}

class TradingViewChart {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.chart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.websocket = null;
        this.isConnected = false;
        this.dataFeed = null;

        // Default configuration
        this.config = {
            symbol: 'BTCUSDT',
            interval: '1m',
            theme: 'dark',
            autoResize: true,
            enableWebSocket: true,
            enableInfiniteHistory: true,
            ...options
        };

        // Chart data
        this.currentData = [];
        this.lastUpdateTime = 0;
        this.lastErrorTime = 0;
        this.isLoadingHistory = false;

        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error(`Container ${this.containerId} not found`);
            return;
        }

        this.createChart();
        this.setupEventListeners();
        this.initializeDataFeed();

        if (this.config.enableInfiniteHistory) {
            this.setupInfiniteHistory();
        }

        if (this.config.enableWebSocket) {
            this.connectWebSocket();
        }
    }

    initializeDataFeed() {
        this.dataFeed = new BinanceDataFeed(this.config.symbol, this.config.interval);
    }

    setupInfiniteHistory() {
        if (!this.chart) return;

        this.chart.timeScale().subscribeVisibleLogicalRangeChange(logicalRange => {
            if (logicalRange && logicalRange.from < 10 && !this.isLoadingHistory) {
                this.loadMoreHistoricalData(logicalRange.from);
            }
        });
    }

    async loadMoreHistoricalData(currentFrom) {
        if (this.isLoadingHistory || !this.dataFeed || !this.dataFeed.hasMoreData) {
            return;
        }

        this.isLoadingHistory = true;
        this.updateStatus('Loading historical data...');

        try {
            const numberBarsToLoad = Math.max(50, 50 - currentFrom);
            console.log(`Loading ${numberBarsToLoad} more historical bars...`);

            const allData = await this.dataFeed.loadMoreData(numberBarsToLoad);

            // Add a small delay to show loading state
            setTimeout(() => {
                this.candlestickSeries.setData(allData);
                this.volumeSeries.setData(this.dataFeed.getVolumeData());

                this.updateStatus(`Loaded ${allData.length} total candles for ${this.config.symbol}`);
                this.isLoadingHistory = false;
            }, 100);

        } catch (error) {
            console.error('Error loading historical data:', error);
            this.updateStatus('Error loading historical data', 'error');
            this.isLoadingHistory = false;
        }
    }
    
    createChart() {
        // Chart options based on TradingView style
        const chartOptions = {
            width: this.container.clientWidth,
            height: this.container.clientHeight || 600,
            layout: {
                backgroundColor: this.config.theme === 'dark' ? '#131722' : '#FFFFFF',
                textColor: this.config.theme === 'dark' ? '#d1d4dc' : '#191919',
                fontSize: 12,
                fontFamily: 'Trebuchet MS, sans-serif',
            },
            grid: {
                vertLines: {
                    color: this.config.theme === 'dark' ? '#363c4e' : '#e1e3e6',
                    style: 1,
                    visible: true,
                },
                horzLines: {
                    color: this.config.theme === 'dark' ? '#363c4e' : '#e1e3e6',
                    style: 1,
                    visible: true,
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
                vertLine: {
                    color: this.config.theme === 'dark' ? '#758696' : '#9598a1',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
                horzLine: {
                    color: this.config.theme === 'dark' ? '#758696' : '#9598a1',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
            },
            rightPriceScale: {
                borderColor: this.config.theme === 'dark' ? '#485c7b' : '#d0d3d7',
                textColor: this.config.theme === 'dark' ? '#b2b5be' : '#191919',
                entireTextOnly: false,
                visible: true,
                scaleMargins: {
                    top: 0.1,
                    bottom: 0.1,
                },
            },
            timeScale: {
                borderColor: this.config.theme === 'dark' ? '#485c7b' : '#d0d3d7',
                textColor: this.config.theme === 'dark' ? '#b2b5be' : '#191919',
                timeVisible: true,
                secondsVisible: false,
                rightOffset: 12,
                barSpacing: 6,
                fixLeftEdge: false,
                lockVisibleTimeRangeOnResize: true,
                rightBarStaysOnScroll: true,
                borderVisible: true,
                visible: true,
            },
            handleScroll: {
                mouseWheel: true,
                pressedMouseMove: true,
                horzTouchDrag: true,
                vertTouchDrag: true,
            },
            handleScale: {
                axisPressedMouseMove: true,
                mouseWheel: true,
                pinch: true,
            },
        };
        
        // Create chart
        this.chart = LightweightCharts.createChart(this.container, chartOptions);
        
        // Create candlestick series
        this.candlestickSeries = this.chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
            priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
            },
        });
        
        // Create volume series
        this.volumeSeries = this.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // Setup crosshair move handler with error protection
        this.chart.subscribeCrosshairMove((param) => {
            try {
                this.handleCrosshairMove(param);
            } catch (error) {
                // Silently handle crosshair errors to prevent console spam
                console.debug('Crosshair error:', error);
            }
        });
        
        console.log('TradingView chart created successfully');
    }
    
    setupEventListeners() {
        if (this.config.autoResize) {
            window.addEventListener('resize', () => {
                this.handleResize();
            });
        }
    }
    
    handleResize() {
        if (this.chart && this.container) {
            this.chart.applyOptions({
                width: this.container.clientWidth,
                height: this.container.clientHeight || 600,
            });
        }
    }
    
    handleCrosshairMove(param) {
        if (!param.time || !param.seriesPrices) {
            this.updateCrosshairInfo(null);
            return;
        }

        try {
            const data = param.seriesPrices.get(this.candlestickSeries);
            const volumeData = param.seriesPrices.get(this.volumeSeries);

            if (data) {
                this.updateCrosshairInfo({
                    time: param.time,
                    open: data.open,
                    high: data.high,
                    low: data.low,
                    close: data.close,
                    volume: volumeData || 0
                });
            }
        } catch (error) {
            // Silently handle crosshair errors to prevent spam
            console.debug('Crosshair move error:', error);
        }
    }
    
    updateCrosshairInfo(data) {
        const infoElement = document.getElementById('crosshair-info');
        if (!infoElement) return;
        
        if (!data) {
            infoElement.textContent = '';
            return;
        }
        
        const time = new Date(data.time * 1000);
        const info = `${time.toLocaleString()} | O: ${data.open?.toFixed(2)} H: ${data.high?.toFixed(2)} L: ${data.low?.toFixed(2)} C: ${data.close?.toFixed(2)} V: ${data.volume?.toLocaleString()}`;
        infoElement.textContent = info;
    }
    
    async loadHistoricalData() {
        try {
            this.updateStatus('Loading initial data...');

            if (!this.dataFeed) {
                this.initializeDataFeed();
            }

            const initialData = await this.dataFeed.loadInitialData(500);

            if (initialData && initialData.length > 0) {
                this.candlestickSeries.setData(initialData);
                this.volumeSeries.setData(this.dataFeed.getVolumeData());
                this.currentData = initialData;

                // Fit content to show all data
                this.chart.timeScale().fitContent();

                console.log(`Loaded ${initialData.length} candles for ${this.config.symbol}`);
                this.updateStatus(`Loaded ${initialData.length} candles for ${this.config.symbol} (scroll left for more history)`);
            } else {
                throw new Error('No data loaded');
            }
        } catch (error) {
            console.error('Error loading historical data:', error);
            this.updateStatus('Error loading data, using sample data');
            this.loadSampleData();
        }
    }

    loadSampleData() {
        try {
            // Generate realistic sample data for demonstration
            const sampleData = [];
            const volumeData = [];

            // Get interval in minutes
            const intervalMinutes = this.getIntervalMinutes(this.config.interval);
            const startTime = Math.floor(Date.now() / 1000) - (500 * intervalMinutes * 60); // 500 intervals ago

            let basePrice = this.config.symbol.includes('BTC') ? 45000 :
                           this.config.symbol.includes('ETH') ? 3000 : 100;

            for (let i = 0; i < 500; i++) {
                const time = startTime + (i * intervalMinutes * 60); // Proper interval spacing
                const volatility = basePrice * 0.002; // 0.2% volatility

                const open = i === 0 ? basePrice : sampleData[i - 1].close;
                const change = (Math.random() - 0.5) * volatility * 2;
                const close = open + change;
                const high = Math.max(open, close) + Math.random() * volatility;
                const low = Math.min(open, close) - Math.random() * volatility;
                const volume = Math.random() * 1000000 + 100000;

                sampleData.push({
                    time: time,
                    open: parseFloat(open.toFixed(2)),
                    high: parseFloat(high.toFixed(2)),
                    low: parseFloat(low.toFixed(2)),
                    close: parseFloat(close.toFixed(2)),
                });

                volumeData.push({
                    time: time,
                    value: parseFloat(volume.toFixed(0)),
                    color: close >= open ? '#26a69a80' : '#ef535080',
                });
            }

            this.candlestickSeries.setData(sampleData);
            this.volumeSeries.setData(volumeData);

            // Fit content to show all data
            this.chart.timeScale().fitContent();

            this.updateStatus(`Sample data loaded for ${this.config.symbol} (${sampleData.length} candles)`);
        } catch (error) {
            console.error('Error loading sample data:', error);
            this.updateStatus('Error loading sample data', 'error');
        }
    }

    getIntervalMinutes(interval) {
        const intervalMap = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
        };
        return intervalMap[interval] || 1;
    }
    
    connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        const wsUrl = `ws://localhost:8000/api/v1/ws/kline?symbol=${this.config.symbol}&interval=${this.config.interval}`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.updateStatus(`Live data connected for ${this.config.symbol}`);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    // Only log parsing errors occasionally to prevent spam
                    if (Date.now() - this.lastErrorTime > 5000) {
                        console.error('Error parsing WebSocket message:', error);
                        this.lastErrorTime = Date.now();
                    }
                }
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateStatus('Live data disconnected');
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connectWebSocket();
                    }
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateStatus('WebSocket connection error', 'error');
            };
            
        } catch (error) {
            console.error('Error creating WebSocket connection:', error);
            this.updateStatus('Failed to connect to live data', 'error');
        }
    }
    
    handleWebSocketMessage(message) {
        try {
            if (message.type === 'kline' && message.data) {
                const klineData = message.data;

                // Ensure timestamp is properly formatted as Unix timestamp in seconds
                let timestamp;
                if (typeof klineData.timestamp === 'number') {
                    // If it's already a number, ensure it's in seconds
                    timestamp = klineData.timestamp > 1000000000000 ?
                        Math.floor(klineData.timestamp / 1000) :
                        Math.floor(klineData.timestamp);
                } else if (typeof klineData.timestamp === 'string') {
                    // If it's a string, parse it
                    timestamp = Math.floor(new Date(klineData.timestamp).getTime() / 1000);
                } else {
                    console.warn('Invalid timestamp format:', klineData.timestamp);
                    return;
                }

                // Validate timestamp is reasonable (not in the past or too far in future)
                const now = Math.floor(Date.now() / 1000);
                if (timestamp < now - 86400 || timestamp > now + 3600) { // Within 24h past to 1h future
                    console.warn('Timestamp out of reasonable range:', timestamp, 'current:', now);
                    return;
                }

                const candlePoint = {
                    time: timestamp,
                    open: parseFloat(klineData.open),
                    high: parseFloat(klineData.high),
                    low: parseFloat(klineData.low),
                    close: parseFloat(klineData.close),
                };

                const volumePoint = {
                    time: timestamp,
                    value: parseFloat(klineData.volume),
                    color: candlePoint.close >= candlePoint.open ? '#26a69a80' : '#ef535080',
                };

                // Validate OHLC data
                if (isNaN(candlePoint.open) || isNaN(candlePoint.high) ||
                    isNaN(candlePoint.low) || isNaN(candlePoint.close)) {
                    console.warn('Invalid OHLC data:', candlePoint);
                    return;
                }

                // Update the chart
                this.candlestickSeries.update(candlePoint);
                this.volumeSeries.update(volumePoint);

                this.lastUpdateTime = Date.now();

                // Update status
                this.updateStatus(`Live update: ${this.config.symbol} $${candlePoint.close.toFixed(2)}`);
            }
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
            // Don't spam the console with repeated errors
            if (this.lastErrorTime !== Date.now()) {
                this.updateStatus('WebSocket data error', 'error');
                this.lastErrorTime = Date.now();
            }
        }
    }
    
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        console.log(`Chart status: ${message}`);
    }
    
    changeSymbol(symbol) {
        this.config.symbol = symbol.toUpperCase();
        this.updateStatus(`Switching to ${symbol}...`);

        // Reset data feed for new symbol
        this.initializeDataFeed();
        this.loadHistoricalData();

        if (this.websocket) {
            this.websocket.close();
            this.connectWebSocket();
        }
    }

    changeInterval(interval) {
        this.config.interval = interval;
        this.updateStatus(`Changing to ${interval} timeframe...`);

        // Reset data feed for new interval
        this.initializeDataFeed();
        this.loadHistoricalData();

        if (this.websocket) {
            this.websocket.close();
            this.connectWebSocket();
        }
    }

    // Advanced chart features
    addPriceLines(levels) {
        /**
         * Add horizontal price lines for support/resistance levels
         * @param {Array} levels - Array of {price, color, style} objects
         */
        if (!levels || !Array.isArray(levels)) return;

        levels.forEach(level => {
            const priceLine = this.candlestickSeries.createPriceLine({
                price: level.price,
                color: level.color || '#758696',
                lineWidth: level.width || 1,
                lineStyle: level.style || 2, // Dashed
                axisLabelVisible: true,
                title: level.title || `${level.price}`,
            });
        });
    }

    addTradingMarkers(trades) {
        /**
         * Add buy/sell markers to the chart
         * @param {Array} trades - Array of trade objects
         */
        if (!trades || !Array.isArray(trades)) return;

        const markers = trades.map(trade => ({
            time: Math.floor(trade.timestamp),
            position: trade.side === 'buy' ? 'belowBar' : 'aboveBar',
            color: trade.side === 'buy' ? '#26a69a' : '#ef5350',
            shape: trade.side === 'buy' ? 'arrowUp' : 'arrowDown',
            text: `${trade.side.toUpperCase()} @ $${trade.price}`,
            size: 1,
        }));

        this.candlestickSeries.setMarkers(markers);
    }

    enableCrosshairMagnet(enabled = true) {
        /**
         * Enable crosshair magnet to snap to candle data points
         */
        this.chart.applyOptions({
            crosshair: {
                ...this.chart.options().crosshair,
                mode: enabled ?
                    LightweightCharts.CrosshairMode.Magnet :
                    LightweightCharts.CrosshairMode.Normal,
            }
        });
    }

    setChartTheme(theme = 'dark') {
        /**
         * Switch between light and dark themes
         */
        const themes = {
            dark: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
                gridColor: '#363c4e',
                borderColor: '#485c7b',
            },
            light: {
                backgroundColor: '#FFFFFF',
                textColor: '#191919',
                gridColor: '#e1e3e6',
                borderColor: '#d0d3d7',
            }
        };

        const selectedTheme = themes[theme] || themes.dark;

        this.chart.applyOptions({
            layout: {
                backgroundColor: selectedTheme.backgroundColor,
                textColor: selectedTheme.textColor,
            },
            grid: {
                vertLines: { color: selectedTheme.gridColor },
                horzLines: { color: selectedTheme.gridColor },
            },
            rightPriceScale: {
                borderColor: selectedTheme.borderColor,
                textColor: selectedTheme.textColor,
            },
            timeScale: {
                borderColor: selectedTheme.borderColor,
                textColor: selectedTheme.textColor,
            },
        });

        this.config.theme = theme;
    }
    
    destroy() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        if (this.chart) {
            this.chart.remove();
        }
        
        window.removeEventListener('resize', this.handleResize);
    }
}

// Global chart instance
let tradingViewChart = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for TradingView library to load
    const initChart = () => {
        if (typeof LightweightCharts !== 'undefined') {
            tradingViewChart = new TradingViewChart('tradingview-chart');
            window.tradingViewChart = tradingViewChart;
            
            // Load initial data
            tradingViewChart.loadHistoricalData();
            
            console.log('TradingView chart initialized');
        } else {
            console.log('Waiting for TradingView library...');
            setTimeout(initChart, 100);
        }
    };
    
    initChart();
});

// Trade Management System

class TradeManager {
    constructor() {
        this.currentMarks = [];
        this.pendingEntry = null;
        this.pendingExit = null;
        this.isMarkingMode = false;
        this.markingType = null;
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        // Trade marking buttons
        document.getElementById('markEntry').addEventListener('click', () => {
            this.startMarking('entry');
        });
        
        document.getElementById('markExit').addEventListener('click', () => {
            this.startMarking('exit');
        });
        
        document.getElementById('completeTrade').addEventListener('click', () => {
            this.completeTrade();
        });
        
        // Chart click handler for marking
        document.addEventListener('chartClick', (event) => {
            if (this.isMarkingMode) {
                this.handleMarkClick(event.detail);
            }
        });
        
        // Export buttons
        document.getElementById('exportMarks').addEventListener('click', () => {
            this.exportMarks();
        });
        
        document.getElementById('exportTrades').addEventListener('click', () => {
            this.exportTrades();
        });
        
        document.getElementById('viewStrategyLog').addEventListener('click', () => {
            this.viewStrategyLog();
        });
    }
    
    startMarking(type) {
        this.isMarkingMode = true;
        this.markingType = type;
        
        // Update UI to show marking mode
        const button = document.getElementById(type === 'entry' ? 'markEntry' : 'markExit');
        button.textContent = `Click on chart to mark ${type}`;
        button.style.backgroundColor = '#FFA726';
        
        // Update chart status
        if (window.tradingViewChart) {
            // Update status in the status bar
            const statusElement = document.getElementById('chart-status');
            if (statusElement) {
                statusElement.textContent = `Click on chart to mark ${type}`;
                statusElement.className = 'status warning';
            }
        }
    }
    
    async handleMarkClick(clickData) {
        if (!this.isMarkingMode) return;
        
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        const entrySide = document.getElementById('entrySide').value;
        
        try {
            // Get indicator snapshot if available
            let indicatorsConfig = null;
            if (indicatorsManager) {
                indicatorsConfig = indicatorsManager.getCurrentConfig();
            }
            
            // Create the mark
            const markData = {
                symbol: symbol,
                timeframe: timeframe,
                timestamp: clickData.timestamp,
                mark_type: this.markingType,
                price: clickData.price,
                indicators_config: indicatorsConfig
            };
            
            if (this.markingType === 'entry') {
                markData.entry_side = entrySide;
            } else if (this.pendingEntry) {
                markData.linked_trade_id = this.pendingEntry.id;
            }
            
            const response = await this.createMark(markData);
            
            if (response.success) {
                const mark = response.data.mark;
                
                // Add marker to chart
                if (window.tradingViewChart && window.tradingViewChart.candlestickSeries) {
                    const color = mark.entry_side === 'buy' ? '#4caf50' : '#f44336';
                    const marker = {
                        time: mark.timestamp,
                        position: 'belowBar',
                        color: color,
                        shape: 'circle',
                        text: `${mark.entry_side.toUpperCase()} @ $${mark.price.toFixed(2)}`,
                        size: 2
                    };

                    const existingMarkers = window.tradingViewChart.candlestickSeries.markers() || [];
                    window.tradingViewChart.candlestickSeries.setMarkers([...existingMarkers, marker]);
                }
                
                // Update pending marks
                if (this.markingType === 'entry') {
                    this.pendingEntry = mark;
                    this.showSuccess(`Entry mark created at ${mark.price.toFixed(4)}`);
                } else {
                    this.pendingExit = mark;
                    this.showSuccess(`Exit mark created at ${mark.price.toFixed(4)}`);
                }
                
                this.currentMarks.push(mark);
                this.updateMarksDisplay();
                
            } else {
                throw new Error('Failed to create mark');
            }
            
        } catch (error) {
            console.error('Error creating mark:', error);
            this.showError(`Error creating mark: ${error.message}`);
        } finally {
            this.stopMarking();
        }
    }
    
    async createMark(markData) {
        const queryParams = new URLSearchParams({
            symbol: markData.symbol,
            timeframe: markData.timeframe,
            timestamp: markData.timestamp,
            mark_type: markData.mark_type,
            price: markData.price.toString()
        });
        
        if (markData.entry_side) {
            queryParams.append('entry_side', markData.entry_side);
        }
        
        if (markData.linked_trade_id) {
            queryParams.append('linked_trade_id', markData.linked_trade_id.toString());
        }
        
        const response = await fetch(`/api/v1/trades/mark?${queryParams}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(markData.indicators_config || {})
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    async completeTrade() {
        if (!this.pendingEntry || !this.pendingExit) {
            this.showError('Both entry and exit marks are required to complete a trade');
            return;
        }
        
        try {
            const response = await fetch('/api/v1/trades/complete-trade', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                params: new URLSearchParams({
                    entry_id: this.pendingEntry.id.toString(),
                    exit_id: this.pendingExit.id.toString()
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(`Trade completed! Profit: ${result.data.profit_pct.toFixed(2)}%`);
                
                // Clear pending marks
                this.pendingEntry = null;
                this.pendingExit = null;
                
                this.updateTradeDisplay(result.data);
                
            } else {
                throw new Error('Failed to complete trade');
            }
            
        } catch (error) {
            console.error('Error completing trade:', error);
            this.showError(`Error completing trade: ${error.message}`);
        }
    }
    
    stopMarking() {
        this.isMarkingMode = false;
        this.markingType = null;
        
        // Reset button states
        document.getElementById('markEntry').textContent = 'Mark Entry';
        document.getElementById('markExit').textContent = 'Mark Exit';
        document.getElementById('markEntry').style.backgroundColor = '';
        document.getElementById('markExit').style.backgroundColor = '';
        
        // Update chart status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Ready';
            statusElement.className = 'status info';
        }
    }
    
    updateMarksDisplay() {
        const marksContainer = document.getElementById('current-marks');
        
        if (this.currentMarks.length === 0) {
            marksContainer.innerHTML = '<p>No marks created</p>';
            return;
        }
        
        let html = '<h4>Current Marks:</h4>';
        this.currentMarks.forEach(mark => {
            const date = new Date(mark.timestamp).toLocaleString();
            html += `
                <div class="mark-item">
                    <strong>${mark.mark_type.toUpperCase()}</strong>
                    ${mark.entry_side ? mark.entry_side.toUpperCase() : ''}
                    @ ${mark.price.toFixed(4)}
                    <br><small>${date}</small>
                </div>
            `;
        });
        
        marksContainer.innerHTML = html;
    }
    
    updateTradeDisplay(tradeData) {
        const summaryContainer = document.getElementById('trade-summary');
        
        const profitClass = tradeData.profit_pct >= 0 ? 'success' : 'error';
        
        summaryContainer.innerHTML = `
            <h4>Last Trade:</h4>
            <div class="trade-summary">
                <p><strong>Entry:</strong> ${tradeData.entry_side.toUpperCase()} @ ${tradeData.entry_price.toFixed(4)}</p>
                <p><strong>Exit:</strong> @ ${tradeData.exit_price.toFixed(4)}</p>
                <p class="${profitClass}"><strong>Profit:</strong> ${tradeData.profit_pct.toFixed(2)}%</p>
                <p><strong>Trade ID:</strong> ${tradeData.trade_id}</p>
            </div>
        `;
    }
    
    async exportMarks() {
        try {
            const symbol = document.getElementById('symbol').value;
            const timeframe = document.getElementById('timeframe').value;
            
            const response = await fetch(`/api/v1/trades/marks?symbol=${symbol}&timeframe=${timeframe}&limit=1000`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.downloadJSON(result.data.marks, `marks_${symbol}_${timeframe}.json`);
                this.showSuccess('Marks exported successfully');
            } else {
                throw new Error('Failed to export marks');
            }
            
        } catch (error) {
            console.error('Error exporting marks:', error);
            this.showError(`Error exporting marks: ${error.message}`);
        }
    }
    
    async exportTrades() {
        try {
            const symbol = document.getElementById('symbol').value;
            const timeframe = document.getElementById('timeframe').value;
            
            const response = await fetch(`/api/v1/trades/strategy-log?symbol=${symbol}&timeframe=${timeframe}&limit=1000`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.downloadJSON(result.data, `trades_${symbol}_${timeframe}.json`);
                this.showSuccess('Trades exported successfully');
            } else {
                throw new Error('Failed to export trades');
            }
            
        } catch (error) {
            console.error('Error exporting trades:', error);
            this.showError(`Error exporting trades: ${error.message}`);
        }
    }
    
    async viewStrategyLog() {
        try {
            const symbol = document.getElementById('symbol').value;
            const timeframe = document.getElementById('timeframe').value;
            
            const response = await fetch(`/api/v1/trades/strategy-log?symbol=${symbol}&timeframe=${timeframe}&limit=100`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showStrategyLogModal(result.data);
            } else {
                throw new Error('Failed to load strategy log');
            }
            
        } catch (error) {
            console.error('Error loading strategy log:', error);
            this.showError(`Error loading strategy log: ${error.message}`);
        }
    }
    
    showStrategyLogModal(data) {
        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modal-body');
        
        let html = `
            <h2>Strategy Log</h2>
            <div class="strategy-summary">
                <h3>Summary</h3>
                <p><strong>Total Trades:</strong> ${data.summary.total_trades}</p>
                <p><strong>Winning Trades:</strong> ${data.summary.winning_trades}</p>
                <p><strong>Losing Trades:</strong> ${data.summary.losing_trades}</p>
                <p><strong>Win Rate:</strong> ${data.summary.win_rate.toFixed(2)}%</p>
                <p><strong>Total Profit:</strong> ${data.summary.total_profit.toFixed(2)}%</p>
                <p><strong>Average Profit:</strong> ${data.summary.avg_profit.toFixed(2)}%</p>
            </div>
            <div class="trades-list">
                <h3>Recent Trades</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #333;">
                            <th style="padding: 10px; border: 1px solid #555;">Date</th>
                            <th style="padding: 10px; border: 1px solid #555;">Side</th>
                            <th style="padding: 10px; border: 1px solid #555;">Entry</th>
                            <th style="padding: 10px; border: 1px solid #555;">Exit</th>
                            <th style="padding: 10px; border: 1px solid #555;">Profit %</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.trades.forEach(trade => {
            const date = new Date(trade.created_at).toLocaleDateString();
            const profitClass = trade.profit_pct >= 0 ? 'color: #4bffb5' : 'color: #ff4976';
            
            html += `
                <tr>
                    <td style="padding: 8px; border: 1px solid #555;">${date}</td>
                    <td style="padding: 8px; border: 1px solid #555;">${trade.entry_side.toUpperCase()}</td>
                    <td style="padding: 8px; border: 1px solid #555;">${trade.entry_ohlcv?.close?.toFixed(4) || 'N/A'}</td>
                    <td style="padding: 8px; border: 1px solid #555;">${trade.exit_ohlcv?.close?.toFixed(4) || 'N/A'}</td>
                    <td style="padding: 8px; border: 1px solid #555; ${profitClass}">${trade.profit_pct.toFixed(2)}%</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
        `;
        
        modalBody.innerHTML = html;
        modal.style.display = 'block';
    }
    
    downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    showSuccess(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="success">${message}</span>`;
        }
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
        }, 3000);
    }
    
    showError(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="error">${message}</span>`;
        }
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
        }, 5000);
    }
}

// Global trade manager instance
let tradeManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    tradeManager = new TradeManager();
    
    // Modal close functionality
    const modal = document.getElementById('modal');
    const closeBtn = document.querySelector('.close');
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
});

"""
Pydantic schemas for manual marks API
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime


class MarkEntry(BaseModel):
    """Schema for creating an entry mark"""
    timestamp: float = Field(..., description="Unix timestamp of the entry")
    price: float = Field(..., description="Entry price")
    side: str = Field(..., description="Entry side: 'buy' or 'sell'")
    quantity: float = Field(..., gt=0, description="Quantity of the position")
    notes: Optional[str] = Field(None, description="Optional notes about the entry")
    ohlcv_data: Optional[Dict[str, Any]] = Field(None, description="OHLCV data at entry time")
    indicator_data: Optional[Dict[str, Any]] = Field(None, description="Indicator data at entry time")

    class Config:
        schema_extra = {
            "example": {
                "timestamp": 1627849200.0,
                "price": 45000.50,
                "side": "buy",
                "quantity": 0.1,
                "notes": "Bullish breakout pattern",
                "ohlcv_data": {
                    "open": 44950.0,
                    "high": 45100.0,
                    "low": 44900.0,
                    "close": 45000.0,
                    "volume": 1500.0
                },
                "indicator_data": {
                    "ema_20": 44800.0,
                    "rsi": 65.5
                }
            }
        }


class MarkExit(BaseModel):
    """Schema for creating an exit mark"""
    entry_id: int = Field(..., description="ID of the entry mark to exit")
    timestamp: float = Field(..., description="Unix timestamp of the exit")
    price: float = Field(..., description="Exit price")
    quantity: float = Field(..., gt=0, description="Quantity to exit")
    notes: Optional[str] = Field(None, description="Optional notes about the exit")
    ohlcv_data: Optional[Dict[str, Any]] = Field(None, description="OHLCV data at exit time")
    indicator_data: Optional[Dict[str, Any]] = Field(None, description="Indicator data at exit time")

    class Config:
        schema_extra = {
            "example": {
                "entry_id": 1,
                "timestamp": 1627935600.0,
                "price": 46500.75,
                "quantity": 0.1,
                "notes": "Target reached",
                "ohlcv_data": {
                    "open": 46400.0,
                    "high": 46600.0,
                    "low": 46350.0,
                    "close": 46500.0,
                    "volume": 1200.0
                },
                "indicator_data": {
                    "ema_20": 45900.0,
                    "rsi": 75.2
                }
            }
        }


class MarkData(BaseModel):
    """Schema for mark data in responses"""
    id: int
    entry_timestamp: float
    entry_price: float
    side: str
    quantity: float
    notes: Optional[str] = None
    exit_timestamp: Optional[float] = None
    exit_price: Optional[float] = None
    exit_quantity: Optional[float] = None
    exit_notes: Optional[str] = None
    pnl: Optional[float] = None
    status: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class MarkResponse(BaseModel):
    """Standard response schema for mark operations"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Entry mark created successfully",
                "data": {
                    "id": 1,
                    "entry_timestamp": 1627849200.0,
                    "entry_price": 45000.50,
                    "side": "buy",
                    "quantity": 0.1,
                    "status": "open"
                }
            }
        }


class MarkListResponse(BaseModel):
    """Response schema for listing marks"""
    success: bool
    message: str
    data: List[Dict[str, Any]]

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Retrieved 5 marks",
                "data": [
                    {
                        "id": 1,
                        "entry_timestamp": 1627849200.0,
                        "entry_price": 45000.50,
                        "side": "buy",
                        "quantity": 0.1,
                        "status": "closed",
                        "exit_timestamp": 1627935600.0,
                        "exit_price": 46500.75,
                        "exit_quantity": 0.1,
                        "pnl": 150.025
                    }
                ]
            }
        }


class StatisticsResponse(BaseModel):
    """Response schema for statistics"""
    success: bool
    data: Dict[str, Any]

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "total_marks": 10,
                    "open_positions": 3,
                    "closed_trades": 7,
                    "win_rate": 71.43,
                    "total_pnl": 1250.75,
                    "winning_trades": 5,
                    "losing_trades": 2
                }
            }
        }

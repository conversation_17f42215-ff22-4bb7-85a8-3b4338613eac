#!/usr/bin/env python3
"""
Check current table structure
"""
import mysql.connector
from contextlib import contextmanager

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '@Oppa121089',
    'database': 'strategy_builder',
    'charset': 'utf8mb4',
    'autocommit': True
}

@contextmanager
def get_db_cursor():
    """Get database cursor with proper connection management"""
    connection = None
    cursor = None
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor(dictionary=True)
        yield cursor
    except Exception as e:
        if connection:
            connection.rollback()
        raise Exception(f"Database operation failed: {e}")
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def check_table_structure():
    """Check the structure of all tables"""
    print("🔍 Checking table structures...")
    
    try:
        with get_db_cursor() as cursor:
            # Check ohlcv_data table
            print("\n📊 OHLCV_DATA table structure:")
            cursor.execute("DESCRIBE ohlcv_data")
            ohlcv_columns = cursor.fetchall()
            for col in ohlcv_columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")
            
            # Check strategies table
            print("\n🎯 STRATEGIES table structure:")
            cursor.execute("DESCRIBE strategies")
            strategy_columns = cursor.fetchall()
            for col in strategy_columns:
                print(f"  - {col['Field']}: {col['Type']} {'(PK)' if col['Key'] == 'PRI' else ''} {'NOT NULL' if col['Null'] == 'NO' else 'NULL'}")
            
            # Check sample data
            print("\n📈 Sample OHLCV data:")
            cursor.execute("SELECT * FROM ohlcv_data LIMIT 3")
            sample_data = cursor.fetchall()
            for row in sample_data:
                print(f"  {row}")
            
            # Check strategies data
            print("\n🎯 Sample strategies data:")
            cursor.execute("SELECT * FROM strategies LIMIT 3")
            strategy_data = cursor.fetchall()
            for row in strategy_data:
                print(f"  {row}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_table_structure()

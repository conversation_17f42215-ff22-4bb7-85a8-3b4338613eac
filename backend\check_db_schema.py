#!/usr/bin/env python3
"""
Check the actual database schema for manual_marks table
"""

import mysql.connector
from app.core.config import settings

def check_table_schema():
    
    try:
        conn = mysql.connector.connect(
            host=settings.DB_HOST,
            user=settings.DB_USERNAME,
            password=settings.DB_PASSWORD.get_secret_value(),
            database=settings.DB_DATABASE
        )
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'manual_marks'")
        table_exists = cursor.fetchone()

        if table_exists:
            print('✅ manual_marks table exists')
            
            # Get table structure
            cursor.execute('DESCRIBE manual_marks')
            columns = cursor.fetchall()
            
            print(f'\nTable has {len(columns)} columns:')
            for col in columns:
                null_status = "NULL" if col[2] == "YES" else "NOT NULL"
                print(f'  - {col[0]}: {col[1]} {null_status}')
                
            # Check for specific columns we need
            column_names = [col[0] for col in columns]
            expected_columns = ['entry_timestamp', 'timestamp', 'entry_price', 'price', 'side', 'quantity']
            
            print(f'\nColumn availability check:')
            for col in expected_columns:
                status = "✅" if col in column_names else "❌"
                print(f'  {status} {col}')
                
        else:
            print('❌ manual_marks table does not exist')

        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'Error checking database: {e}')

if __name__ == '__main__':
    check_table_schema()

"""
Technical Indicators API Endpoints
"""
from fastapi import APIRouter, HTTPException, Query, Body
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from app.core.data_access import OHLCVDataAccess, IndicatorsDataAccess
from app.services.indicators import IndicatorsService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/calculate")
async def calculate_indicators(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    indicators_config: Dict[str, Dict] = Body(..., description="Indicators configuration"),
    limit: int = Query(500, description="Number of candles to use", ge=1, le=2000),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)"),
    store_results: bool = Query(True, description="Store results in database"),
    db: Session = Depends(get_db)
):
    """Calculate technical indicators for given OHLCV data"""
    try:
        # Get OHLCV data from database
        query = db.query(OHLCVData).filter(
            OHLCVData.symbol == symbol.upper(),
            OHLCVData.timeframe == timeframe
        )
        
        # Apply time filters if provided
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(OHLCVData.timestamp >= start_dt)
        
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(OHLCVData.timestamp <= end_dt)
        
        # Get data ordered by timestamp
        ohlcv_records = query.order_by(OHLCVData.timestamp.asc()).limit(limit).all()
        
        if not ohlcv_records:
            raise HTTPException(status_code=404, detail="No OHLCV data found")
        
        # Convert to list of dictionaries
        ohlcv_data = [record.to_dict() for record in ohlcv_records]
        
        # Calculate indicators
        indicators = IndicatorsService.calculate_all_indicators(ohlcv_data, indicators_config)
        
        if not indicators:
            raise HTTPException(status_code=500, detail="Failed to calculate indicators")
        
        # Store results in database if requested
        if store_results:
            stored_count = 0
            timestamps = indicators.get('timestamps', [])
            
            for i, timestamp_str in enumerate(timestamps):
                timestamp = datetime.fromisoformat(timestamp_str)
                
                for indicator_name, values in indicators.items():
                    if indicator_name == 'timestamps':
                        continue
                    
                    # Prepare indicator value
                    if isinstance(values, list):
                        if i < len(values):
                            indicator_value = values[i]
                        else:
                            continue
                    elif isinstance(values, dict):
                        indicator_value = {}
                        for key, value_list in values.items():
                            if i < len(value_list):
                                indicator_value[key] = value_list[i]
                    else:
                        continue
                    
                    # Check if record already exists
                    existing = db.query(IndicatorsData).filter(
                        IndicatorsData.symbol == symbol.upper(),
                        IndicatorsData.timeframe == timeframe,
                        IndicatorsData.timestamp == timestamp,
                        IndicatorsData.indicator_name == indicator_name
                    ).first()
                    
                    if not existing:
                        indicator_record = IndicatorsData(
                            symbol=symbol.upper(),
                            timeframe=timeframe,
                            timestamp=timestamp,
                            indicator_name=indicator_name,
                            value=indicator_value
                        )
                        db.add(indicator_record)
                        stored_count += 1
            
            db.commit()
            logger.info(f"Stored {stored_count} indicator records")
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "indicators_config": indicators_config,
                "data_points": len(ohlcv_data),
                "indicators": indicators,
                "stored_records": stored_count if store_results else 0
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating indicators: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/data")
async def get_indicators_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    indicator_name: Optional[str] = Query(None, description="Specific indicator name"),
    limit: int = Query(500, description="Number of records to return", ge=1, le=2000),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)"),
    db: Session = Depends(get_db)
):
    """Get stored indicators data from database"""
    try:
        query = db.query(IndicatorsData).filter(
            IndicatorsData.symbol == symbol.upper(),
            IndicatorsData.timeframe == timeframe
        )
        
        if indicator_name:
            query = query.filter(IndicatorsData.indicator_name == indicator_name)
        
        # Apply time filters if provided
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(IndicatorsData.timestamp >= start_dt)
        
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(IndicatorsData.timestamp <= end_dt)
        
        # Order by timestamp and apply limit
        indicators_records = query.order_by(IndicatorsData.timestamp.desc()).limit(limit).all()
        
        if not indicators_records:
            raise HTTPException(status_code=404, detail="No indicators data found")
        
        # Reverse to get chronological order
        indicators_records.reverse()
        
        # Convert to response format
        data = [record.to_dict() for record in indicators_records]
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "indicator_name": indicator_name,
                "count": len(data),
                "indicators": data
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicators data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/available")
async def get_available_indicators():
    """Get list of available technical indicators"""
    return {
        "success": True,
        "data": {
            "indicators": {
                "rsi": {
                    "name": "Relative Strength Index",
                    "parameters": {
                        "period": {"type": "int", "default": 14, "min": 2, "max": 100}
                    }
                },
                "macd": {
                    "name": "Moving Average Convergence Divergence",
                    "parameters": {
                        "fast": {"type": "int", "default": 12, "min": 1, "max": 50},
                        "slow": {"type": "int", "default": 26, "min": 1, "max": 100},
                        "signal": {"type": "int", "default": 9, "min": 1, "max": 50}
                    }
                },
                "ema": {
                    "name": "Exponential Moving Average",
                    "parameters": {
                        "period": {"type": "int", "default": 20, "min": 1, "max": 200}
                    }
                },
                "sma": {
                    "name": "Simple Moving Average",
                    "parameters": {
                        "period": {"type": "int", "default": 50, "min": 1, "max": 200}
                    }
                },
                "bollinger_bands": {
                    "name": "Bollinger Bands",
                    "parameters": {
                        "period": {"type": "int", "default": 20, "min": 1, "max": 100},
                        "std": {"type": "float", "default": 2.0, "min": 0.1, "max": 5.0}
                    }
                }
            }
        }
    }

@router.post("/snapshot")
async def get_indicator_snapshot(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    timestamp: str = Query(..., description="Timestamp (ISO format)"),
    indicators_config: Dict[str, Dict] = Body(..., description="Indicators configuration"),
    db: Session = Depends(get_db)
):
    """Get indicator values at a specific timestamp"""
    try:
        # Get OHLCV data up to the specified timestamp
        target_timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        query = db.query(OHLCVData).filter(
            OHLCVData.symbol == symbol.upper(),
            OHLCVData.timeframe == timeframe,
            OHLCVData.timestamp <= target_timestamp
        ).order_by(OHLCVData.timestamp.asc()).limit(1000)  # Get enough data for indicators
        
        ohlcv_records = query.all()
        
        if not ohlcv_records:
            raise HTTPException(status_code=404, detail="No OHLCV data found")
        
        # Convert to list of dictionaries
        ohlcv_data = [record.to_dict() for record in ohlcv_records]
        
        # Get indicator values at the timestamp
        snapshot = IndicatorsService.get_indicator_value_at_timestamp(
            ohlcv_data, timestamp, indicators_config
        )
        
        return {
            "success": True,
            "data": {
                "symbol": symbol.upper(),
                "timeframe": timeframe,
                "timestamp": timestamp,
                "indicators_config": indicators_config,
                "snapshot": snapshot
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting indicator snapshot: {e}")
        raise HTTPException(status_code=500, detail=str(e))

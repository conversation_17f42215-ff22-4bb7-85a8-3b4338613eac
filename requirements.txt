# FastAPI Backend
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.10.0
python-multipart>=0.0.6

# Database
pymysql>=1.1.0
SQLAlchemy>=2.0.23
alembic>=1.13.1
cryptography>=41.0.0

# Technical Analysis (Python 3.12 compatible)
pandas>=2.1.4
numpy>=1.24.0,<1.26.0
pandas-ta>=0.3.14b0

# API Clients
requests>=2.31.0
websocket-client>=1.6.4

# Utilities
python-dotenv>=1.0.0
python-dateutil>=2.8.2
pytz>=2023.3

# Windows Compatibility
setuptools>=68.0.0
wheel>=0.41.0

# Development and Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.25.2
flake8>=6.1.0
mypy>=1.7.0
bandit>=1.7.5
black>=23.11.0
isort>=5.12.0
psutil>=5.9.6

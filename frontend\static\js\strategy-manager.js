/**
 * Strategy Management System
 * Handles strategy creation, selection, and data linking
 */

class StrategyManager {
    constructor() {
        this.currentStrategy = null;
        this.strategies = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadStrategies();
        this.setDefaultDates();
    }

    setupEventListeners() {
        // Strategy creation
        document.getElementById('create-strategy').addEventListener('click', () => {
            this.createStrategy();
        });

        // Strategy selection
        document.getElementById('strategy-select').addEventListener('change', (e) => {
            this.selectStrategy(e.target.value);
        });

        // Enhanced fetch data with strategy linking
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchDataWithStrategy();
        });

        // Load data from database
        document.getElementById('loadData').addEventListener('click', () => {
            this.loadDataFromDatabase();
        });

        // Enter key support for strategy name
        document.getElementById('new-strategy-name').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.createStrategy();
            }
        });
    }

    setDefaultDates() {
        // Set default end date to now
        const now = new Date();
        const endDate = now.toISOString().slice(0, 16);
        document.getElementById('end-date').value = endDate;

        // Set default start date to 30 days ago
        const startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        document.getElementById('start-date').value = startDate.toISOString().slice(0, 16);
    }

    async loadStrategies() {
        try {
            const response = await fetch('/api/v1/strategies/list');
            const strategies = await response.json();
            
            this.strategies = strategies;
            this.updateStrategyDropdown();
            
            console.log(`Loaded ${strategies.length} strategies`);
        } catch (error) {
            console.error('Error loading strategies:', error);
            this.showMessage('Error loading strategies', 'error');
        }
    }

    updateStrategyDropdown() {
        const select = document.getElementById('strategy-select');
        
        // Clear existing options except the first one
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // Add strategy options
        this.strategies.forEach(strategy => {
            const option = document.createElement('option');
            option.value = strategy.id;
            option.textContent = `${strategy.name} (${strategy.symbol}/${strategy.timeframe}) - ${strategy.data_count} candles`;
            select.appendChild(option);
        });
    }

    async createStrategy() {
        const nameInput = document.getElementById('new-strategy-name');
        const name = nameInput.value.trim();
        
        if (!name) {
            this.showMessage('Please enter a strategy name', 'error');
            return;
        }

        const symbol = document.getElementById('symbol').value.trim().toUpperCase();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;

        if (!symbol) {
            this.showMessage('Please enter a symbol', 'error');
            return;
        }

        try {
            const response = await fetch('/api/v1/strategies/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: name,
                    description: `Strategy for ${symbol} on ${timeframe} timeframe`,
                    symbol: symbol,
                    timeframe: timeframe,
                    exchange: exchange
                })
            });

            if (response.ok) {
                const strategy = await response.json();
                this.showMessage(`Strategy "${strategy.name}" created successfully!`, 'success');
                
                // Clear input and reload strategies
                nameInput.value = '';
                await this.loadStrategies();
                
                // Auto-select the new strategy
                document.getElementById('strategy-select').value = strategy.id;
                this.selectStrategy(strategy.id);
                
            } else {
                const error = await response.json();
                this.showMessage(`Error creating strategy: ${error.detail}`, 'error');
            }
        } catch (error) {
            console.error('Error creating strategy:', error);
            this.showMessage('Error creating strategy', 'error');
        }
    }

    selectStrategy(strategyId) {
        if (!strategyId) {
            this.currentStrategy = null;
            this.updateLoadButton(false);
            return;
        }

        this.currentStrategy = this.strategies.find(s => s.id == strategyId);
        if (this.currentStrategy) {
            // Update form fields to match strategy
            document.getElementById('symbol').value = this.currentStrategy.symbol;
            document.getElementById('timeframe').value = this.currentStrategy.timeframe;
            document.getElementById('exchange').value = this.currentStrategy.exchange;
            
            // Update header if chart exists
            if (window.professionalChart) {
                document.getElementById('header-symbol').textContent = this.currentStrategy.symbol;
                document.getElementById('main-symbol-input').value = this.currentStrategy.symbol;
            }

            // Check if strategy has data to enable load button
            this.updateLoadButton(this.currentStrategy.data_count > 0);
            
            this.showMessage(`Selected strategy: ${this.currentStrategy.name}`, 'info');
        }
    }

    async fetchDataWithStrategy() {
        if (!this.currentStrategy) {
            this.showMessage('Please select or create a strategy first', 'error');
            return;
        }

        const symbol = document.getElementById('symbol').value.trim().toUpperCase();
        const timeframe = document.getElementById('timeframe').value;
        const exchange = document.getElementById('exchange').value;
        const limit = parseInt(document.getElementById('limit').value);
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        if (!symbol || !startDate || !endDate) {
            this.showMessage('Please fill in all required fields', 'error');
            return;
        }

        // Validate date range
        if (new Date(startDate) >= new Date(endDate)) {
            this.showMessage('Start date must be before end date', 'error');
            return;
        }

        try {
            this.showMessage('Fetching data from exchange...', 'info');
            document.getElementById('fetchData').disabled = true;

            const response = await fetch('/api/v1/ohlcv/fetch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    symbol: symbol,
                    timeframe: timeframe,
                    exchange: exchange,
                    limit: limit,
                    start_time: new Date(startDate).toISOString(),
                    end_time: new Date(endDate).toISOString(),
                    strategy_id: this.currentStrategy.id
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showFetchResult(result.message, 'success');
                
                // Reload strategies to update data count
                await this.loadStrategies();
                
                // Update current strategy data count
                const updatedStrategy = this.strategies.find(s => s.id === this.currentStrategy.id);
                if (updatedStrategy) {
                    this.currentStrategy = updatedStrategy;
                    this.updateLoadButton(true);
                }
                
                // Update chart if available
                if (window.professionalChart && result.data && result.data.ohlcv) {
                    this.loadDataToChart(result.data.ohlcv);
                }
                
            } else {
                this.showFetchResult(`Error: ${result.error || result.message}`, 'error');
            }
            
        } catch (error) {
            console.error('Error fetching data:', error);
            this.showFetchResult('Error fetching data from exchange', 'error');
        } finally {
            document.getElementById('fetchData').disabled = false;
        }
    }

    async loadDataFromDatabase() {
        if (!this.currentStrategy) {
            this.showMessage('Please select a strategy first', 'error');
            return;
        }

        if (this.currentStrategy.data_count === 0) {
            this.showMessage('No data available for this strategy. Fetch data first.', 'error');
            return;
        }

        try {
            this.showMessage('Loading data from database...', 'info');
            
            const response = await fetch(`/api/v1/ohlcv/data?symbol=${this.currentStrategy.symbol}&timeframe=${this.currentStrategy.timeframe}&limit=5000&strategy_id=${this.currentStrategy.id}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.ohlcv) {
                this.loadDataToChart(result.data.ohlcv);
                this.showMessage(`Loaded ${result.data.ohlcv.length} candles from database`, 'success');
            } else {
                this.showMessage('No data found in database', 'error');
            }
            
        } catch (error) {
            console.error('Error loading data from database:', error);
            this.showMessage('Error loading data from database', 'error');
        }
    }

    loadDataToChart(ohlcvData) {
        if (!window.professionalChart) {
            console.warn('Chart not available');
            return;
        }

        try {
            // Process data for chart
            const candleData = ohlcvData.map(item => ({
                time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                open: parseFloat(item.open),
                high: parseFloat(item.high),
                low: parseFloat(item.low),
                close: parseFloat(item.close),
            })).sort((a, b) => a.time - b.time);

            const volumeData = ohlcvData.map(item => ({
                time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                value: parseFloat(item.volume),
                color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a80' : '#ef535080',
            })).sort((a, b) => a.time - b.time);

            // Update chart
            window.professionalChart.candlestickSeries.setData(candleData);
            window.professionalChart.volumeSeries.setData(volumeData);
            window.professionalChart.chart.timeScale().fitContent();

            // Pass data to indicators manager
            if (window.indicatorsManager) {
                window.indicatorsManager.setCurrentData(ohlcvData);
            }

            console.log(`Loaded ${candleData.length} candles to chart`);
            
        } catch (error) {
            console.error('Error loading data to chart:', error);
            this.showMessage('Error displaying data on chart', 'error');
        }
    }

    updateLoadButton(hasData) {
        const loadButton = document.getElementById('loadData');
        loadButton.disabled = !hasData;
        
        if (hasData) {
            loadButton.textContent = 'Load from DB';
            loadButton.title = 'Load data from database';
        } else {
            loadButton.textContent = 'No Data';
            loadButton.title = 'No data available. Fetch data first.';
        }
    }

    showMessage(message, type = 'info') {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        console.log(`Strategy Manager: ${message}`);
    }

    showFetchResult(message, type = 'info') {
        const resultElement = document.getElementById('fetch-result');
        if (resultElement) {
            resultElement.textContent = message;
            resultElement.className = `fetch-result ${type}`;
            resultElement.style.display = 'block';
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                resultElement.style.display = 'none';
            }, 10000);
        }
        
        this.showMessage(message, type);
    }

    getCurrentStrategy() {
        return this.currentStrategy;
    }

    getStrategies() {
        return this.strategies;
    }
}

// Global strategy manager instance
window.strategyManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.strategyManager = new StrategyManager();
    console.log('Strategy Manager initialized');
});

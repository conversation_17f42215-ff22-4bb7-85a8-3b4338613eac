"""
Input validation utilities
"""
from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime
import re


class SymbolValidator(BaseModel):
    """Symbol validation"""
    symbol: str = Field(..., min_length=1, max_length=20, description="Trading pair symbol")
    
    @field_validator('symbol')
    @classmethod
    def validate_symbol(cls, v):
        if not v or not v.strip():
            raise ValueError('Symbol cannot be empty')

        # Clean the symbol first
        cleaned = v.upper().strip()

        # Basic symbol format validation (letters and numbers only)
        if not re.match(r'^[A-Z0-9]+$', cleaned):
            raise ValueError('Symbol must contain only letters and numbers')

        return cleaned


class TimeframeValidator(BaseModel):
    """Timeframe validation"""
    timeframe: str = Field(..., description="Chart timeframe")
    
    @field_validator('timeframe')
    @classmethod
    def validate_timeframe(cls, v):
        valid_timeframes = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M']
        if v not in valid_timeframes:
            raise ValueError(f'Invalid timeframe. Must be one of: {valid_timeframes}')
        return v


class ExchangeValidator(BaseModel):
    """Exchange validation"""
    exchange: str = Field(..., description="Exchange name")
    
    @field_validator('exchange')
    @classmethod
    def validate_exchange(cls, v):
        valid_exchanges = ['binance', 'mexc']
        if v.lower() not in valid_exchanges:
            raise ValueError(f'Invalid exchange. Must be one of: {valid_exchanges}')
        return v.lower()


class LimitValidator(BaseModel):
    """Limit validation"""
    limit: int = Field(..., ge=1, le=5000, description="Number of records to fetch")


class DateTimeValidator(BaseModel):
    """DateTime validation"""
    start_time: Optional[datetime] = Field(None, description="Start time")
    end_time: Optional[datetime] = Field(None, description="End time")
    
    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        if v and info.data.get('start_time'):
            if v <= info.data['start_time']:
                raise ValueError('End time must be after start time')
        return v


class OHLCVFetchRequest(SymbolValidator, TimeframeValidator, ExchangeValidator, LimitValidator, DateTimeValidator):
    """OHLCV data fetch request validation"""
    strategy_id: Optional[int] = Field(None, description="Strategy ID to link data to")


class IndicatorsConfigValidator(BaseModel):
    """Technical indicators configuration validation"""
    rsi: Optional[Dict[str, Any]] = Field(None, description="RSI configuration")
    macd: Optional[Dict[str, Any]] = Field(None, description="MACD configuration")
    ema: Optional[Dict[str, Any]] = Field(None, description="EMA configuration")
    sma: Optional[Dict[str, Any]] = Field(None, description="SMA configuration")
    bollinger_bands: Optional[Dict[str, Any]] = Field(None, description="Bollinger Bands configuration")
    
    @field_validator('rsi')
    @classmethod
    def validate_rsi(cls, v):
        if v is not None:
            period = v.get('period', 14)
            if not isinstance(period, int) or period < 2 or period > 100:
                raise ValueError('RSI period must be an integer between 2 and 100')
        return v
    
    @field_validator('macd')
    @classmethod
    def validate_macd(cls, v):
        if v is not None:
            fast = v.get('fast', 12)
            slow = v.get('slow', 26)
            signal = v.get('signal', 9)

            if not all(isinstance(x, int) for x in [fast, slow, signal]):
                raise ValueError('MACD parameters must be integers')

            if not (1 <= fast <= 50 and 1 <= slow <= 100 and 1 <= signal <= 50):
                raise ValueError('MACD parameters out of valid range')

            if fast >= slow:
                raise ValueError('MACD fast period must be less than slow period')
        return v
    
    @field_validator('ema')
    @classmethod
    def validate_ema(cls, v):
        if v is not None:
            period = v.get('period', 20)
            if not isinstance(period, int) or period < 1 or period > 200:
                raise ValueError('EMA period must be an integer between 1 and 200')
        return v

    @field_validator('sma')
    @classmethod
    def validate_sma(cls, v):
        if v is not None:
            period = v.get('period', 50)
            if not isinstance(period, int) or period < 1 or period > 200:
                raise ValueError('SMA period must be an integer between 1 and 200')
        return v

    @field_validator('bollinger_bands')
    @classmethod
    def validate_bollinger_bands(cls, v):
        if v is not None:
            period = v.get('period', 20)
            std = v.get('std', 2.0)

            if not isinstance(period, int) or period < 1 or period > 100:
                raise ValueError('Bollinger Bands period must be an integer between 1 and 100')

            if not isinstance(std, (int, float)) or std < 0.1 or std > 5.0:
                raise ValueError('Bollinger Bands standard deviation must be between 0.1 and 5.0')
        return v


class TradeMarkRequest(SymbolValidator, TimeframeValidator):
    """Trade mark request validation"""
    timestamp: datetime = Field(..., description="Mark timestamp")
    mark_type: str = Field(..., description="Mark type (entry or exit)")
    price: float = Field(..., gt=0, description="Price at mark")
    entry_side: Optional[str] = Field(None, description="Entry side (buy or sell)")
    linked_trade_id: Optional[int] = Field(None, description="Linked trade ID")
    
    @field_validator('mark_type')
    @classmethod
    def validate_mark_type(cls, v):
        if v.lower() not in ['entry', 'exit']:
            raise ValueError('Mark type must be either "entry" or "exit"')
        return v.lower()

    @field_validator('entry_side')
    @classmethod
    def validate_entry_side(cls, v, info):
        if info.data.get('mark_type') == 'entry' and not v:
            raise ValueError('Entry side is required for entry marks')
        if v and v.lower() not in ['buy', 'sell']:
            raise ValueError('Entry side must be either "buy" or "sell"')
        return v.lower() if v else None


class PaginationValidator(BaseModel):
    """Pagination validation"""
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(50, ge=1, le=1000, description="Items per page")
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.page_size

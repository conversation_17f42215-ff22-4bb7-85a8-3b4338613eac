#!/usr/bin/env python3
"""
Test script to verify Binance API connectivity and data fetching
"""
import asyncio
import sys
import os
from datetime import datetime

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.services.binance_client import BinanceClient
from backend.app.core.exceptions import ExchangeError, ValidationError


async def test_binance_connection():
    """Test Binance API connection and data fetching"""
    print("🚀 Testing Binance API Connection...")
    print("=" * 50)
    
    client = BinanceClient()
    
    try:
        # Test 1: Connection test
        print("1. Testing connection...")
        connection_ok = await client.test_connection()
        if connection_ok:
            print("   ✅ Connection successful")
        else:
            print("   ❌ Connection failed")
            return False
            
        # Test 2: Server time
        print("\n2. Getting server time...")
        server_time = await client.get_server_time()
        server_timestamp = server_time.get('serverTime', 0)
        server_datetime = datetime.fromtimestamp(server_timestamp / 1000)
        print(f"   ✅ Server time: {server_datetime}")
        
        # Test 3: Fetch BTCUSDT klines
        print("\n3. Fetching BTCUSDT 1m klines (last 10 candles)...")
        klines = await client.get_klines(
            symbol='BTCUSDT',
            interval='1m',
            limit=10
        )
        
        if klines:
            print(f"   ✅ Fetched {len(klines)} candles")
            print("   📊 Sample data:")
            for i, kline in enumerate(klines[-3:]):  # Show last 3 candles
                print(f"      {i+1}. Time: {kline['timestamp']}")
                print(f"         OHLC: {kline['open']:.2f} | {kline['high']:.2f} | {kline['low']:.2f} | {kline['close']:.2f}")
                print(f"         Volume: {kline['volume']:.2f}")
        else:
            print("   ❌ No klines data received")
            return False
            
        # Test 4: Fetch ETHUSDT klines
        print("\n4. Fetching ETHUSDT 5m klines (last 5 candles)...")
        eth_klines = await client.get_klines(
            symbol='ETHUSDT',
            interval='5m',
            limit=5
        )
        
        if eth_klines:
            print(f"   ✅ Fetched {len(eth_klines)} ETH candles")
            latest = eth_klines[-1]
            print(f"   📈 Latest ETH price: ${latest['close']:.2f}")
        else:
            print("   ❌ No ETH klines data received")
            
        # Test 5: Test different timeframes
        print("\n5. Testing different timeframes...")
        timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        
        for tf in timeframes:
            try:
                tf_klines = await client.get_klines(
                    symbol='BTCUSDT',
                    interval=tf,
                    limit=3
                )
                if tf_klines:
                    latest_price = tf_klines[-1]['close']
                    print(f"   ✅ {tf}: ${latest_price:.2f}")
                else:
                    print(f"   ❌ {tf}: No data")
            except Exception as e:
                print(f"   ❌ {tf}: Error - {e}")
                
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("✅ Binance API is working correctly")
        print("✅ Ready for TradingView chart integration")
        return True
        
    except ExchangeError as e:
        print(f"\n❌ Exchange Error: {e}")
        return False
    except ValidationError as e:
        print(f"\n❌ Validation Error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected Error: {e}")
        return False


async def test_popular_symbols():
    """Test fetching data for popular trading symbols"""
    print("\n🔥 Testing Popular Trading Symbols...")
    print("=" * 50)
    
    client = BinanceClient()
    popular_symbols = [
        'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
        'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
    ]
    
    successful = 0
    for symbol in popular_symbols:
        try:
            klines = await client.get_klines(
                symbol=symbol,
                interval='1h',
                limit=1
            )
            
            if klines:
                price = klines[0]['close']
                print(f"✅ {symbol:<10} ${price:>10.4f}")
                successful += 1
            else:
                print(f"❌ {symbol:<10} No data")
                
        except Exception as e:
            print(f"❌ {symbol:<10} Error: {str(e)[:30]}...")
            
    print(f"\n📊 Successfully fetched data for {successful}/{len(popular_symbols)} symbols")
    return successful > 0


async def main():
    """Main test function"""
    print("🚀 Binance API Integration Test")
    print("Testing real-time data fetching for TradingView charts")
    print("=" * 60)
    
    # Test basic connection and functionality
    basic_test_ok = await test_binance_connection()
    
    if basic_test_ok:
        # Test popular symbols
        await test_popular_symbols()
        
        print("\n" + "=" * 60)
        print("🎯 INTEGRATION READY!")
        print("✅ Binance API is working")
        print("✅ Data fetching is functional")
        print("✅ Multiple symbols supported")
        print("✅ Multiple timeframes supported")
        print("\n🚀 You can now:")
        print("   1. Start the server: python scripts/run_server.py")
        print("   2. Open: http://localhost:8000/static/tradingview-demo.html")
        print("   3. Enjoy real Binance data in TradingView charts!")
        
    else:
        print("\n" + "=" * 60)
        print("❌ INTEGRATION NOT READY")
        print("❌ Binance API connection failed")
        print("❌ Please check your internet connection")
        print("❌ Or try again later")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test failed with error: {e}")
        sys.exit(1)

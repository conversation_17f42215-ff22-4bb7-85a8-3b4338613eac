/* Strategy Builder CSS Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Trebuchet MS', Arial, sans-serif;
    background-color: #131722;
    color: #d1d4dc;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #2962ff 0%, #1e88e5 100%);
    border-radius: 8px;
    border: 1px solid #363c4e;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.controls-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #1e222d;
    border-radius: 8px;
    border: 1px solid #363c4e;
    align-items: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: 600;
    font-size: 0.9em;
    color: #cccccc;
}

.control-group input,
.control-group select {
    padding: 8px 12px;
    border: 1px solid #444;
    border-radius: 5px;
    background-color: #333;
    color: #fff;
    font-size: 14px;
    min-width: 120px;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.indicators-panel {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 10px;
}

.indicators-panel h3 {
    margin-bottom: 15px;
    color: #667eea;
}

.indicators-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
}

.indicator-group {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background-color: #333;
    border-radius: 5px;
}

.indicator-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 600;
    min-width: 60px;
}

.indicator-group input[type="checkbox"] {
    min-width: auto;
    width: 16px;
    height: 16px;
}

.indicator-group input[type="number"] {
    width: 60px;
    min-width: 60px;
}

.chart-container {
    margin-bottom: 20px;
    background-color: #1e222d;
    border-radius: 8px;
    border: 1px solid #363c4e;
    overflow: hidden;
}

.chart {
    height: 600px;
    width: 100%;
    background-color: #131722;
    position: relative;
}

.chart-info {
    padding: 15px;
    background-color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

#chart-status {
    color: #667eea;
    font-weight: 600;
}

#crosshair-info {
    color: #cccccc;
}

.trade-panel {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 10px;
}

.trade-panel h3 {
    margin-bottom: 15px;
    color: #667eea;
}

.trade-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.trade-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

#current-marks,
#trade-summary {
    padding: 15px;
    background-color: #333;
    border-radius: 5px;
    min-height: 100px;
}

.data-panel {
    padding: 20px;
    background-color: #2a2a2a;
    border-radius: 10px;
}

.data-panel h3 {
    margin-bottom: 15px;
    color: #667eea;
}

.export-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: #2a2a2a;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 20px;
    top: 15px;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #fff;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #333;
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error {
    color: #ff6b6b;
    background-color: rgba(255, 107, 107, 0.1);
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #ff6b6b;
}

.success {
    color: #51cf66;
    background-color: rgba(81, 207, 102, 0.1);
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #51cf66;
}

.info {
    color: #74c0fc;
    background-color: rgba(116, 192, 252, 0.1);
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #74c0fc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .controls-panel {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        width: 100%;
    }
    
    .indicators-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .trade-info {
        grid-template-columns: 1fr;
    }
    
    .chart {
        height: 400px;
    }
}

"""
Data access layer using PyMySQL
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from app.core.database import get_db_cursor, DatabaseError

logger = logging.getLogger(__name__)


class OHLCVDataAccess:
    """OHLCV data access methods"""
    
    @staticmethod
    def insert_ohlcv(symbol: str, timeframe: str, timestamp: datetime, 
                     open_price: float, high: float, low: float, close: float, volume: float) -> int:
        """Insert OHLCV data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT IGNORE INTO ohlcv_data 
                (symbol, timeframe, timestamp, open, high, low, close, volume)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (symbol, timeframe, timestamp, open_price, high, low, close, volume))
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Failed to insert OHLCV data: {e}")
            raise DatabaseError(f"OHLCV insert failed: {e}")
    
    @staticmethod
    def get_ohlcv_data(symbol: str, timeframe: str, limit: int = 500, 
                       start_time: Optional[datetime] = None, 
                       end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get OHLCV data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                SELECT symbol, timeframe, timestamp, open, high, low, close, volume, created_at
                FROM ohlcv_data 
                WHERE symbol = %s AND timeframe = %s
                """
                params = [symbol, timeframe]
                
                if start_time:
                    sql += " AND timestamp >= %s"
                    params.append(start_time)
                
                if end_time:
                    sql += " AND timestamp <= %s"
                    params.append(end_time)
                
                sql += " ORDER BY timestamp DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Failed to get OHLCV data: {e}")
            raise DatabaseError(f"OHLCV query failed: {e}")
    
    @staticmethod
    def get_symbols() -> List[str]:
        """Get available symbols"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute("SELECT DISTINCT symbol FROM ohlcv_data ORDER BY symbol")
                return [row['symbol'] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Failed to get symbols: {e}")
            raise DatabaseError(f"Symbols query failed: {e}")


class IndicatorsDataAccess:
    """Indicators data access methods"""
    
    @staticmethod
    def insert_indicators(symbol: str, timeframe: str, timestamp: datetime,
                         indicator_name: str, config: Dict[str, Any], values: Dict[str, Any]) -> int:
        """Insert indicator data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO indicators_data 
                (symbol, timeframe, timestamp, indicator_name, indicator_config, indicator_values)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                indicator_config = VALUES(indicator_config),
                indicator_values = VALUES(indicator_values)
                """
                cursor.execute(sql, (symbol, timeframe, timestamp, indicator_name, 
                                   json.dumps(config), json.dumps(values)))
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Failed to insert indicators: {e}")
            raise DatabaseError(f"Indicators insert failed: {e}")
    
    @staticmethod
    def get_indicators(symbol: str, timeframe: str, indicator_name: str, 
                      limit: int = 500) -> List[Dict[str, Any]]:
        """Get indicator data"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                SELECT timestamp, indicator_config, indicator_values
                FROM indicators_data 
                WHERE symbol = %s AND timeframe = %s AND indicator_name = %s
                ORDER BY timestamp DESC LIMIT %s
                """
                cursor.execute(sql, (symbol, timeframe, indicator_name, limit))
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    result['indicator_config'] = json.loads(result['indicator_config'])
                    result['indicator_values'] = json.loads(result['indicator_values'])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get indicators: {e}")
            raise DatabaseError(f"Indicators query failed: {e}")


class TradeDataAccess:
    """Trade data access methods"""
    
    @staticmethod
    def insert_mark(symbol: str, timeframe: str, mark_type: str, entry_side: Optional[str],
                   timestamp: datetime, price: float, indicator_snapshot: Optional[Dict] = None,
                   ohlcv_snapshot: Optional[Dict] = None, linked_trade_id: Optional[int] = None) -> int:
        """Insert trade mark"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO manual_marks 
                (symbol, timeframe, mark_type, entry_side, timestamp, price, 
                 indicator_snapshot, ohlcv_snapshot, linked_trade_id)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    json.dumps(indicator_snapshot) if indicator_snapshot else None,
                    json.dumps(ohlcv_snapshot) if ohlcv_snapshot else None,
                    linked_trade_id
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Failed to insert trade mark: {e}")
            raise DatabaseError(f"Trade mark insert failed: {e}")
    
    @staticmethod
    def get_marks(symbol: Optional[str] = None, timeframe: Optional[str] = None,
                 mark_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trade marks"""
        try:
            with get_db_cursor() as cursor:
                sql = "SELECT * FROM manual_marks WHERE 1=1"
                params = []
                
                if symbol:
                    sql += " AND symbol = %s"
                    params.append(symbol)
                
                if timeframe:
                    sql += " AND timeframe = %s"
                    params.append(timeframe)
                
                if mark_type:
                    sql += " AND mark_type = %s"
                    params.append(mark_type)
                
                sql += " ORDER BY timestamp DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    if result['indicator_snapshot']:
                        result['indicator_snapshot'] = json.loads(result['indicator_snapshot'])
                    if result['ohlcv_snapshot']:
                        result['ohlcv_snapshot'] = json.loads(result['ohlcv_snapshot'])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get trade marks: {e}")
            raise DatabaseError(f"Trade marks query failed: {e}")
    
    @staticmethod
    def get_mark_by_id(mark_id: int) -> Optional[Dict[str, Any]]:
        """Get trade mark by ID"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute("SELECT * FROM manual_marks WHERE id = %s", (mark_id,))
                result = cursor.fetchone()
                
                if result:
                    if result['indicator_snapshot']:
                        result['indicator_snapshot'] = json.loads(result['indicator_snapshot'])
                    if result['ohlcv_snapshot']:
                        result['ohlcv_snapshot'] = json.loads(result['ohlcv_snapshot'])
                
                return result
        except Exception as e:
            logger.error(f"Failed to get trade mark by ID: {e}")
            raise DatabaseError(f"Trade mark query failed: {e}")
    
    @staticmethod
    def insert_strategy_log(symbol: str, timeframe: str, entry_id: int, exit_id: int,
                           entry_side: str, profit_pct: float, entry_ohlcv: Optional[Dict] = None,
                           exit_ohlcv: Optional[Dict] = None, entry_indicators: Optional[Dict] = None,
                           exit_indicators: Optional[Dict] = None) -> int:
        """Insert strategy log entry"""
        try:
            with get_db_cursor() as cursor:
                sql = """
                INSERT INTO strategy_log 
                (symbol, timeframe, entry_id, exit_id, entry_side, profit_pct,
                 entry_ohlcv, exit_ohlcv, entry_indicator_snapshot, exit_indicator_snapshot)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    symbol, timeframe, entry_id, exit_id, entry_side, profit_pct,
                    json.dumps(entry_ohlcv) if entry_ohlcv else None,
                    json.dumps(exit_ohlcv) if exit_ohlcv else None,
                    json.dumps(entry_indicators) if entry_indicators else None,
                    json.dumps(exit_indicators) if exit_indicators else None
                ))
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Failed to insert strategy log: {e}")
            raise DatabaseError(f"Strategy log insert failed: {e}")
    
    @staticmethod
    def get_strategy_log(symbol: Optional[str] = None, timeframe: Optional[str] = None,
                        limit: int = 100) -> List[Dict[str, Any]]:
        """Get strategy log"""
        try:
            with get_db_cursor() as cursor:
                sql = "SELECT * FROM strategy_log WHERE 1=1"
                params = []
                
                if symbol:
                    sql += " AND symbol = %s"
                    params.append(symbol)
                
                if timeframe:
                    sql += " AND timeframe = %s"
                    params.append(timeframe)
                
                sql += " ORDER BY created_at DESC LIMIT %s"
                params.append(limit)
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # Parse JSON fields
                for result in results:
                    for field in ['entry_ohlcv', 'exit_ohlcv', 'entry_indicator_snapshot', 'exit_indicator_snapshot']:
                        if result[field]:
                            result[field] = json.loads(result[field])
                
                return results
        except Exception as e:
            logger.error(f"Failed to get strategy log: {e}")
            raise DatabaseError(f"Strategy log query failed: {e}")

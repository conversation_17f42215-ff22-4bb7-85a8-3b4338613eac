#!/usr/bin/env python3
"""
Simple system test for Strategy Builder
"""
import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

def test_indicators():
    """Test indicators service"""
    print("🧪 Testing Indicators Service...")
    
    try:
        from app.services.indicators import IndicatorsService
        from datetime import datetime, timedelta
        
        # Create test data
        data = []
        for i in range(50):
            data.append({
                'timestamp': datetime.now() + timedelta(hours=i),
                'open': 50000 + i * 10,
                'high': 50100 + i * 10,
                'low': 49900 + i * 10,
                'close': 50000 + i * 10,
                'volume': 1000
            })
        
        # Test indicators
        config = {'rsi': {'period': 14}, 'ema': {'period': 20}}
        result = IndicatorsService.calculate_all_indicators(data, config)
        
        if result and 'rsi' in result and 'ema' in result:
            print(f"✅ Indicators test passed!")
            print(f"   RSI values: {len(result.get('rsi', []))} points")
            print(f"   EMA values: {len(result.get('ema', []))} points")
            return True
        else:
            print("❌ Indicators test failed - no results")
            return False
            
    except Exception as e:
        print(f"❌ Indicators test failed: {e}")
        return False

def test_database():
    """Test database connection"""
    print("\n🧪 Testing Database Connection...")
    
    try:
        from app.core.database import test_connection
        import asyncio
        
        result = asyncio.run(test_connection())
        if result:
            print("✅ Database test passed!")
            return True
        else:
            print("❌ Database test failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_exchanges():
    """Test exchange connections"""
    print("\n🧪 Testing Exchange Connections...")
    
    try:
        from app.services.binance_client import BinanceClient
        from app.services.mexc_client import MEXCClient
        
        # Test Binance
        binance = BinanceClient()
        binance_ok = binance.test_connection()
        
        # Test MEXC
        mexc = MEXCClient()
        mexc_ok = mexc.test_connection()
        
        if binance_ok:
            print("✅ Binance connection test passed!")
        else:
            print("⚠️ Binance connection test failed")
            
        if mexc_ok:
            print("✅ MEXC connection test passed!")
        else:
            print("⚠️ MEXC connection test failed")
        
        return binance_ok or mexc_ok
        
    except Exception as e:
        print(f"❌ Exchange test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Strategy Builder System Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Indicators
    if test_indicators():
        tests_passed += 1
    
    # Test 2: Database
    if test_database():
        tests_passed += 1
    
    # Test 3: Exchanges
    if test_exchanges():
        tests_passed += 1
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! System is ready!")
        return True
    else:
        print("⚠️ Some tests failed. Check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

// Technical Indicators Management

class IndicatorsManager {
    constructor() {
        this.currentConfig = {};
        this.currentData = null;
        
        this.initEventListeners();
    }
    
    initEventListeners() {
        document.getElementById('calculateIndicators').addEventListener('click', () => {
            this.calculateIndicators();
        });
        
        // Update config when checkboxes change
        const checkboxes = document.querySelectorAll('input[type="checkbox"][id$="-enabled"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateConfig();
            });
        });
        
        // Update config when parameters change
        const paramInputs = document.querySelectorAll('.indicator-group input[type="number"]');
        paramInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateConfig();
            });
        });
    }
    
    updateConfig() {
        this.currentConfig = {};
        
        // RSI
        if (document.getElementById('rsi-enabled').checked) {
            this.currentConfig.rsi = {
                period: parseInt(document.getElementById('rsi-period').value) || 14
            };
        }
        
        // MACD
        if (document.getElementById('macd-enabled').checked) {
            this.currentConfig.macd = {
                fast: parseInt(document.getElementById('macd-fast').value) || 12,
                slow: parseInt(document.getElementById('macd-slow').value) || 26,
                signal: parseInt(document.getElementById('macd-signal').value) || 9
            };
        }
        
        // EMA
        if (document.getElementById('ema-enabled').checked) {
            this.currentConfig.ema = {
                period: parseInt(document.getElementById('ema-period').value) || 20
            };
        }
        
        // SMA
        if (document.getElementById('sma-enabled').checked) {
            this.currentConfig.sma = {
                period: parseInt(document.getElementById('sma-period').value) || 50
            };
        }
        
        // Bollinger Bands
        if (document.getElementById('bollinger-enabled')?.checked) {
            this.currentConfig.bollinger_bands = {
                period: parseInt(document.getElementById('bollinger-period')?.value) || 20,
                std: parseFloat(document.getElementById('bollinger-std')?.value) || 2.0
            };
        }
    }
    
    async calculateIndicators() {
        if (!this.currentData) {
            this.showError('No OHLCV data available. Please fetch data first.');
            return;
        }
        
        this.updateConfig();
        
        if (Object.keys(this.currentConfig).length === 0) {
            this.showError('No indicators selected. Please enable at least one indicator.');
            return;
        }
        
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        const limit = parseInt(document.getElementById('limit').value);
        
        try {
            this.showLoading('Calculating indicators...');
            
            const response = await fetch('/api/v1/indicators/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.currentConfig),
                params: new URLSearchParams({
                    symbol: symbol,
                    timeframe: timeframe,
                    limit: limit.toString(),
                    store_results: 'true'
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess(`Indicators calculated successfully! ${result.data.stored_records} records stored.`);
                
                // Update multi-panel chart with indicators
                if (window.multiPanelChartManager && result.data.indicators) {
                    window.multiPanelChartManager.loadIndicators(result.data.indicators);
                }

                // Fallback to legacy chart manager
                if (window.chartManager && result.data.indicators) {
                    window.chartManager.updateIndicators(result.data.indicators);
                }

                // Store indicators data for trade marking
                this.currentIndicators = result.data.indicators;

                // Create sub-panels for RSI and MACD (legacy support)
                this.createSubPanels(result.data.indicators);
                
            } else {
                throw new Error('Failed to calculate indicators');
            }
            
        } catch (error) {
            console.error('Error calculating indicators:', error);
            this.showError(`Error calculating indicators: ${error.message}`);
        }
    }
    
    createSubPanels(indicators) {
        // This would create separate chart panels for RSI and MACD
        // For now, we'll just log the data
        console.log('Indicators data:', indicators);
        
        // In a full implementation, you would create separate chart instances
        // or use the lightweight-charts library's ability to create sub-panels
    }
    
    async getIndicatorSnapshot(timestamp) {
        if (!this.currentConfig || Object.keys(this.currentConfig).length === 0) {
            return null;
        }
        
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        
        try {
            const response = await fetch('/api/v1/indicators/snapshot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.currentConfig),
                params: new URLSearchParams({
                    symbol: symbol,
                    timeframe: timeframe,
                    timestamp: timestamp
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.success) {
                return result.data.snapshot;
            } else {
                throw new Error('Failed to get indicator snapshot');
            }
            
        } catch (error) {
            console.error('Error getting indicator snapshot:', error);
            return null;
        }
    }
    
    setCurrentData(data) {
        this.currentData = data;
    }
    
    getCurrentConfig() {
        this.updateConfig();
        return this.currentConfig;
    }
    
    showLoading(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="loading"></span> ${message}`;
        }
    }
    
    showSuccess(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="success">${message}</span>`;
        }
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
        }, 3000);
    }
    
    showError(message) {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.innerHTML = `<span class="error">${message}</span>`;
        }
        
        setTimeout(() => {
            statusElement.textContent = 'Ready';
        }, 5000);
    }
}

// Global indicators manager instance
let indicatorsManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    indicatorsManager = new IndicatorsManager();
});

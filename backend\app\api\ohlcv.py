"""
Enhanced OHLCV Data API Endpoints with proper validation and error handling
"""
from fastapi import APIRouter, HTTPException, Query, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import asyncio

from app.core.database import DatabaseError
from app.core.data_access import OHLCVDataAccess
from app.core.exceptions import ExchangeError, ValidationError, RateLimitError
from app.core.validators import OHLCVFetchRequest, SymbolValidator, TimeframeValidator, ExchangeValidator
from app.services.binance_client import BinanceClient
from app.services.mexc_client import MEXCClient

logger = logging.getLogger(__name__)
router = APIRouter()

# Response models for better API documentation
class OHLCVResponse(BaseModel):
    """OHLCV response model"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class HealthResponse(BaseModel):
    """Health check response model"""
    success: bool
    exchange: str
    message: str
    rate_limit_status: Optional[Dict[str, Any]] = None

@router.post("/fetch", response_model=OHLCVResponse)
async def fetch_ohlcv_data(request: OHLCVFetchRequest) -> OHLCVResponse:
    """
    Fetch OHLCV data from exchange and store in database

    This endpoint fetches candlestick data from the specified exchange
    and stores it in the database, avoiding duplicates.
    """
    try:
        # Initialize exchange client
        if request.exchange == "binance":
            client = BinanceClient()
        elif request.exchange == "mexc":
            client = MEXCClient()
        else:
            raise ValidationError(f"Unsupported exchange: {request.exchange}")

        # Fetch data from exchange
        ohlcv_data = await client.get_klines(
            symbol=request.symbol,
            interval=request.timeframe,
            limit=request.limit,
            start_time=request.start_time,
            end_time=request.end_time
        )

        if not ohlcv_data:
            return OHLCVResponse(
                success=False,
                message="No data found",
                error="No data available for the specified parameters"
            )

        # Store in database with batch processing
        stored_count = await _store_ohlcv_data(request.symbol, request.timeframe, ohlcv_data)

        logger.info(f"Fetched {len(ohlcv_data)} candles, stored {stored_count} new records for {request.symbol}")

        return OHLCVResponse(
            success=True,
            message=f"Successfully fetched {len(ohlcv_data)} candles, stored {stored_count} new records",
            data={
                "symbol": request.symbol,
                "timeframe": request.timeframe,
                "exchange": request.exchange,
                "total_fetched": len(ohlcv_data),
                "new_records": stored_count,
                "data_range": {
                    "start": ohlcv_data[0]['timestamp'].isoformat() if ohlcv_data else None,
                    "end": ohlcv_data[-1]['timestamp'].isoformat() if ohlcv_data else None
                }
            }
        )

    except ValidationError as e:
        logger.warning(f"Validation error: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    except RateLimitError as e:
        logger.warning(f"Rate limit error: {e}")
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded. Retry after {e.retry_after} seconds" if e.retry_after else "Rate limit exceeded"
        )

    except ExchangeError as e:
        logger.error(f"Exchange error: {e}")
        raise HTTPException(status_code=status.HTTP_502_BAD_GATEWAY, detail=f"Exchange error: {e.message}")

    except DatabaseError as e:
        logger.error(f"Database error: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database operation failed")

    except Exception as e:
        logger.error(f"Unexpected error fetching OHLCV data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")

async def _store_ohlcv_data(symbol: str, timeframe: str, ohlcv_data: List[Dict]) -> int:
    """Store OHLCV data in database with batch processing"""
    stored_count = 0

    try:
        for data in ohlcv_data:
            count = OHLCVDataAccess.insert_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                timestamp=data['timestamp'],
                open_price=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close'],
                volume=data['volume']
            )
            stored_count += count

        return stored_count

    except Exception as e:
        logger.error(f"Error storing OHLCV data: {e}")
        raise DatabaseError(f"Failed to store OHLCV data: {e}")


@router.get("/data", response_model=OHLCVResponse)
async def get_ohlcv_data(
    symbol: str = Query(..., description="Trading pair symbol"),
    timeframe: str = Query(..., description="Timeframe"),
    limit: int = Query(500, ge=1, le=2000, description="Number of records"),
    start_time: Optional[str] = Query(None, description="Start time (ISO format)"),
    end_time: Optional[str] = Query(None, description="End time (ISO format)")
) -> OHLCVResponse:
    """Get OHLCV data from database"""
    try:
        # Validate inputs
        symbol_validator = SymbolValidator(symbol=symbol)
        timeframe_validator = TimeframeValidator(timeframe=timeframe)

        # Parse timestamps if provided
        start_dt = None
        end_dt = None
        if start_time:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        if end_time:
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))

        # Get data from database
        ohlcv_data = OHLCVDataAccess.get_ohlcv_data(
            symbol=symbol_validator.symbol,
            timeframe=timeframe_validator.timeframe,
            limit=limit,
            start_time=start_dt,
            end_time=end_dt
        )

        if not ohlcv_data:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No data found")

        return OHLCVResponse(
            success=True,
            message=f"Retrieved {len(ohlcv_data)} OHLCV records",
            data={
                "symbol": symbol_validator.symbol,
                "timeframe": timeframe_validator.timeframe,
                "count": len(ohlcv_data),
                "ohlcv": ohlcv_data
            }
        )

    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except DatabaseError as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Database error")
    except Exception as e:
        logger.error(f"Error getting OHLCV data: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.get("/test-connection", response_model=HealthResponse)
async def test_exchange_connection(
    exchange: str = Query(..., description="Exchange name (binance or mexc)")
) -> HealthResponse:
    """Test exchange API connection"""
    try:
        exchange_validator = ExchangeValidator(exchange=exchange)

        if exchange_validator.exchange == "binance":
            client = BinanceClient()
        elif exchange_validator.exchange == "mexc":
            client = MEXCClient()
        else:
            raise ValidationError(f"Unsupported exchange: {exchange}")

        # Test connection
        connection_ok = await client.test_connection()

        # Get rate limit status if available
        rate_limit_status = None
        if hasattr(client, 'get_rate_limit_status'):
            rate_limit_status = client.get_rate_limit_status()

        return HealthResponse(
            success=connection_ok,
            exchange=exchange_validator.exchange,
            message="Connection successful" if connection_ok else "Connection failed",
            rate_limit_status=rate_limit_status
        )

    except ValidationError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error testing exchange connection: {e}")
        return HealthResponse(
            success=False,
            exchange=exchange,
            message=f"Connection test failed: {e}"
        )


@router.get("/symbols")
async def get_symbols(
    exchange: Optional[str] = Query(None, description="Exchange name")
):
    """Get available symbols"""
    try:
        # Get symbols from database
        db_symbols = OHLCVDataAccess.get_symbols()

        result = {
            "success": True,
            "data": {
                "database_symbols": db_symbols,
                "exchange_symbols": []
            }
        }

        # Get symbols from exchange if specified
        if exchange:
            exchange_validator = ExchangeValidator(exchange=exchange)

            if exchange_validator.exchange == "binance":
                client = BinanceClient()
            elif exchange_validator.exchange == "mexc":
                client = MEXCClient()
            else:
                raise ValidationError(f"Unsupported exchange: {exchange}")

            try:
                # This would require implementing get_symbols method in clients
                # For now, just return empty list
                result["data"]["exchange_symbols"] = []
                result["data"]["exchange"] = exchange_validator.exchange
            except Exception as e:
                logger.warning(f"Failed to get symbols from {exchange}: {e}")

        return result

    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")
